"use client"
import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import Typography from "@/components/ui/typography"
import { FiMail } from "react-icons/fi"
import { useTheme } from "next-themes"
import { Hi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, HiDesktopComputer, HiPhone } from "react-icons/hi"

function Support() {
  const router = useRouter()
  const { setTheme } = useTheme()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
      {/* Theme Toggle Buttons */}
      <div className="absolute top-5 right-5">
        <div className="flex space-x-2">
          <button onClick={() => setTheme("light")} aria-label="Light Mode">
            <HiSun size={24} className="text-yellow-500" />
          </button>
          <button onClick={() => setTheme("dark")} aria-label="Dark Mode">
            <HiMoon size={24} className="text-gray-500" />
          </button>
          <button onClick={() => setTheme("system")} aria-label="System Mode">
            <HiDesktopComputer size={24} className="text-blue-500" />
          </button>
        </div>
      </div>

      {/* Support Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-md text-center">
        {/* Support Icon */}
        <div className="flex justify-center mb-6">
          <FiMail size={48} className="text-blue-600 dark:text-blue-400" />
        </div>

        {/* Title */}
        <Typography
          text="Need Help? Contact Support"
          variant="h2"
          className="text-2xl font-bold mb-4 text-gray-800 dark:text-white"
        />
        
        {/* Support Message */}
        <Typography
          text="Our support team is here to assist you. If you have any questions or need help, please reach out to us."
          variant="p"
          className="mb-6 text-gray-600 dark:text-gray-400"
        />

        {/* Contact Options */}
        <div className="space-y-3">
          <div className="flex items-center justify-center space-x-2">
            <FiMail className="text-blue-500" size={20} />
            <a href="mailto:<EMAIL>" className="text-blue-500 hover:underline">
              <EMAIL>
            </a>
          </div>
          <div className="flex items-center justify-center space-x-2">
            <HiPhone className="text-green-500" size={20} />
            <a href="tel:+1234567890" className="text-green-500 hover:underline">
              ****** 567 890
            </a>
          </div>
        </div>

        {/* Buttons */}
        <div className="mt-6">
          <Button
            className="bg-blue-600 dark:text-white hover:bg-blue-500 dark:bg-blue-700 dark:hover:bg-blue-600 text-white px-6 py-3 rounded-lg mb-4"
          >
            Contact Support
          </Button>
          <Button
            onClick={() => router.push("/")}
            className="bg-gray-400 dark:text-white hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-white px-6 py-3 rounded-lg"
          >
            Back to Home
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Support
