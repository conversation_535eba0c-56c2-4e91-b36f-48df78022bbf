import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '../ui/select';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell, TableFooter } from '../ui/table';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '../ui/dropdown-menu';
import { Plus, Search, Upload, Download, MoreHorizontal, Edit, Eye, Trash2, ToggleLeft, ToggleRight } from 'lucide-react';

interface ChartOfAccountsProps {
  accountSearchTerm: string;
  setAccountSearchTerm: (term: string) => void;
  accountFilterType: string;
  setAccountFilterType: (type: string) => void;
  accountFilterStatus: string;
  setAccountFilterStatus: (status: string) => void;
  accountSortBy: string;
  setAccountSortBy: (sort: string) => void;
  accountSortOrder: string;
  setAccountSortOrder: (order: string) => void;
  accounts: any[];
  selectedAccountIds: string[];
  setSelectedAccountIds: (ids: string[]) => void;
  selectAllAccounts: boolean;
  setSelectAllAccounts: (checked: boolean) => void;
  handleEditAccount: (account: any) => void;
  handleDeleteAccount: (id: string) => void;
  handleToggleAccountStatus: (id: string) => void;
  handleViewAccountLedger: (account: any) => void;
  handleViewAccountHistory: (account: any) => void;
  handleBulkAccountAction: (action: string, accountIds: string[]) => void;
  handleExportAccounts: (format: string) => void;
  handleImportAccounts: (file: File) => void;
  formatCurrency: (amount: number, currency?: string) => string;
  setShowAccountDialog: (show: boolean) => void;
  setShowAccountImportDialog: (show: boolean) => void;
  setShowAccountExportDialog: (show: boolean) => void;
}

const ChartOfAccounts: React.FC<ChartOfAccountsProps> = ({
  accountSearchTerm,
  setAccountSearchTerm,
  accountFilterType,
  setAccountFilterType,
  accountFilterStatus,
  setAccountFilterStatus,
  accountSortBy,
  setAccountSortBy,
  accountSortOrder,
  setAccountSortOrder,
  accounts,
  selectedAccountIds,
  setSelectedAccountIds,
  selectAllAccounts,
  setSelectAllAccounts,
  handleEditAccount,
  handleDeleteAccount,
  handleToggleAccountStatus,
  handleViewAccountLedger,
  handleViewAccountHistory,
  handleBulkAccountAction,
  handleExportAccounts,
  handleImportAccounts,
  formatCurrency,
  setShowAccountDialog,
  setShowAccountImportDialog,
  setShowAccountExportDialog
}) => {
  return (
    <>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Chart of Accounts</h2>
          <p className="text-sm text-gray-600">Manage your organization's chart of accounts</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowAccountImportDialog(true)}>
            <Upload size={16} className="mr-2" />
            Import
          </Button>
          <Button variant="outline" onClick={() => setShowAccountExportDialog(true)}>
            <Download size={16} className="mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowAccountDialog(true)}>
            <Plus size={16} className="mr-2" />
            New Account
          </Button>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-3 text-gray-400" />
                <Input
                  placeholder="Search accounts by name, code, or category..."
                  value={accountSearchTerm}
                  onChange={(e) => setAccountSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={accountFilterType} onValueChange={setAccountFilterType}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Account Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="asset">Assets</SelectItem>
                  <SelectItem value="liability">Liabilities</SelectItem>
                  <SelectItem value="equity">Equity</SelectItem>
                  <SelectItem value="revenue">Revenue</SelectItem>
                  <SelectItem value="expense">Expenses</SelectItem>
                </SelectContent>
              </Select>
              <Select value={accountFilterStatus} onValueChange={setAccountFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <div>
                <p className="text-sm text-gray-600">Assets</p>
                <p className="text-xl font-bold">{formatCurrency(accounts.filter(a => a.type === 'asset').reduce((sum, a) => sum + a.balance, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div>
                <p className="text-sm text-gray-600">Liabilities</p>
                <p className="text-xl font-bold">{formatCurrency(accounts.filter(a => a.type === 'liability').reduce((sum, a) => sum + a.balance, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="text-sm text-gray-600">Equity</p>
                <p className="text-xl font-bold">{formatCurrency(accounts.filter(a => a.type === 'equity').reduce((sum, a) => sum + a.balance, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <div>
                <p className="text-sm text-gray-600">Revenue</p>
                <p className="text-xl font-bold">{formatCurrency(accounts.filter(a => a.type === 'revenue').reduce((sum, a) => sum + a.balance, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <div>
                <p className="text-sm text-gray-600">Expenses</p>
                <p className="text-xl font-bold">{formatCurrency(accounts.filter(a => a.type === 'expense').reduce((sum, a) => sum + a.balance, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Accounts Table */}
      <Card>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-lg font-semibold">Chart of Accounts</h3>
              <p className="text-sm text-gray-600">Manage your organization's accounts</p>
            </div>
            <div className="flex gap-2">
              {selectedAccountIds.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal size={16} className="mr-2" />
                      Actions ({selectedAccountIds.length})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleBulkAccountAction('activate', selectedAccountIds)}>
                      <ToggleRight size={16} className="mr-2" />
                      Activate Accounts
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAccountAction('deactivate', selectedAccountIds)}>
                      <ToggleLeft size={16} className="mr-2" />
                      Deactivate Accounts
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAccountAction('delete', selectedAccountIds)}>
                      <Trash2 size={16} className="mr-2" />
                      Delete Accounts
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectAllAccounts}
                    onChange={(e) => {
                      const isChecked = e.target.checked;
                      setSelectAllAccounts(isChecked);
                      if (isChecked) {
                        setSelectedAccountIds(accounts.map(a => a.id));
                      } else {
                        setSelectedAccountIds([]);
                      }
                    }}
                    aria-label="Select all accounts"
                    className="rounded"
                  />
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setAccountSortBy('code');
                    setAccountSortOrder(accountSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Code {accountSortBy === 'code' && (accountSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setAccountSortBy('name');
                    setAccountSortOrder(accountSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Name {accountSortBy === 'name' && (accountSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setAccountSortBy('type');
                    setAccountSortOrder(accountSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Type {accountSortBy === 'type' && (accountSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Category</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setAccountSortBy('balance');
                    setAccountSortOrder(accountSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Balance {accountSortBy === 'balance' && (accountSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {accounts.map((account) => (
                <TableRow key={account.id} className={selectedAccountIds.includes(account.id) ? 'bg-blue-50' : ''}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedAccountIds.includes(account.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedAccountIds([...selectedAccountIds, account.id]);
                        } else {
                          setSelectedAccountIds(selectedAccountIds.filter(id => id !== account.id));
                          setSelectAllAccounts(false);
                        }
                      }}
                      aria-label={`Select account ${account.code}`}
                      className="rounded"
                    />
                  </TableCell>
                  <TableCell className="font-medium">{account.code}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{account.name}</div>
                      {account.description && (
                        <div className="text-sm text-gray-600">{account.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={
                      account.type === 'asset' ? 'bg-blue-100 text-blue-800' :
                      account.type === 'liability' ? 'bg-red-100 text-red-800' :
                      account.type === 'equity' ? 'bg-green-100 text-green-800' :
                      account.type === 'revenue' ? 'bg-purple-100 text-purple-800' :
                      'bg-orange-100 text-orange-800'
                    }>
                      {account.type}
                    </Badge>
                  </TableCell>
                  <TableCell>{account.category}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(account.balance)}</TableCell>
                  <TableCell>
                    <Badge className={account.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                      {account.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal size={14} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleViewAccountLedger(account)}>
                          <Eye size={14} className="mr-2" />
                          View Ledger
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewAccountHistory(account)}>
                          <Eye size={14} className="mr-2" />
                          View History
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditAccount(account)}>
                          <Edit size={14} className="mr-2" />
                          Edit Account
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleToggleAccountStatus(account.id)}>
                          {account.isActive ? (
                            <>
                              <ToggleLeft size={14} className="mr-2" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <ToggleRight size={14} className="mr-2" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-red-600"
                          onClick={() => handleDeleteAccount(account.id)}
                        >
                          <Trash2 size={14} className="mr-2" />
                          Delete Account
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell colSpan={8} className="text-center text-sm text-gray-500">
                  Showing {accounts.length} accounts
                  {selectedAccountIds.length > 0 && ` • ${selectedAccountIds.length} selected`}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default ChartOfAccounts;
