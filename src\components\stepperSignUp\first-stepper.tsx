import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSignupValues } from '@/hooks/signup-values';
import Typography from '../ui/typography';

const industries = [
    "Logistic Management",
    "Law Firm",
    "Event Management",
    "Project Management",
    "Construction",
    "Retail, Shops, Goods",
    "Others"
];

const currencies = [
    { value: "AOA", label: "Angola - Kwanza (AOA)" },
    { value: "ZAR", label: "South Africa - Rand (ZAR)" },
    { value: "USD", label: "Congo - US Dollar (USD)" }
];

const languages = [
    { value: "French", label: "French" },
    { value: "Portuguese", label: "Portuguese" },
    { value: "English", label: "English" }
];

const formSchema = z
  .object({
    industry:    z.string().nonempty("Please select an industry"),
    companyName: z.string().nonempty("Company name is required"),
    country:     z.enum(["Angola", "South Africa", "Congo"], { required_error: "Country is required" }),
    city:        z.string().nonempty("City is required"),
    address:     z.string().nonempty("Address is required"),
    cellNumber:  z.string().nonempty("Cell number is required"),
    currency:    z.string().nonempty("Currency is required"),
    language:    z.string().nonempty("Language is required"),
  })
  .refine((data) => {
    const num = data.cellNumber.trim();
    switch (data.country) {
      case "South Africa":
        
        return /^(?:0|\+27)\d{9}$/.test(num);
      case "Angola":
        
        return /^(?:0|\+244)\d{9}$/.test(num);
      case "Congo":
        
        return /^(?:0|\+242)\d{9}$/.test(num);
      default:
        return false;
    }
  }, {
    path: ["cellNumber"],
    message: "Cell number must match the format for the chosen country",
  });

const FirstStepper = () => {
    const { setCurrentStep, updateFormData, formData  } = useSignupValues();

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: formData
    });

    const handleSubmit = (data: z.infer<typeof formSchema>) => {
        updateFormData(data); 
        setCurrentStep(2); 
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
                
                <Typography
                    text="Step 1: Your Business Information"
                    variant="h2"
                    className="text-2xl text-center font-bold text-blue-600 dark:text-blue-400"
                />
                <Typography
                    text="Please provide the following information about your business."
                    variant="p"
                    className="opacity-90 mb-7 text-gray-600 dark:text-gray-400 text-center"
                />
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {/* Industry Dropdown */}
                    <FormField
                        name="industry"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Industry Name</label>
                                <FormControl>
                                    <select
                                        {...field}
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg p-2"
                                    >
                                        <option value="">Select an Industry</option>
                                        {industries.map((industry) => (
                                            <option key={industry} value={industry}>
                                                {industry}
                                            </option>
                                        ))}
                                    </select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Company Name */}
                    <FormField
                        name="companyName"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Company Name</label>
                                <FormControl>
                                    <Input
                                        {...field}
                                        placeholder="Enter your company name"
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField name="country" render={({ field }) => (
                    <FormItem>
                        <label>Country</label>
                        <FormControl>
                        <select {...field} className="w-full p-2 border rounded">
                            <option value="">Select a Country</option>
                            <option value="South Africa">South Africa</option>
                            <option value="Angola">Angola</option>
                            <option value="Congo">Congo</option>
                        </select>
                        </FormControl>
                        <FormMessage/>
                    </FormItem>
                    )}/>


                    {/* City */}
                    <FormField
                        name="city"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">City</label>
                                <FormControl>
                                    <Input
                                        {...field}
                                        placeholder="Enter your city"
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Address */}
                    <FormField
                        name="address"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Address</label>
                                <FormControl>
                                    <Input
                                        {...field}
                                        placeholder="Enter your address"
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Cell Number */}
                    <FormField
                        name="cellNumber"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Cell Number</label>
                                <FormControl>
                                    <Input
                                        {...field}
                                        placeholder="Enter your cell number"
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Currency Dropdown */}
                    <FormField
                        name="currency"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Currency</label>
                                <FormControl>
                                    <select
                                        {...field}
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg p-2"
                                    >
                                        <option value="">Select a Currency</option>
                                        {currencies.map(({ value, label }) => (
                                            <option key={value} value={value}>{label}</option>
                                        ))}
                                    </select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Language Dropdown */}
                    <FormField
                        name="language"
                        render={({ field }) => (
                            <FormItem>
                                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Language</label>
                                <FormControl>
                                    <select
                                        {...field}
                                        className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg p-2"
                                    >
                                        <option value="" >Select a Language</option>
                                        {languages.map(({ value, label }) => (
                                            <option key={value} value={value}>{label}</option>
                                        ))}
                                    </select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Submit Button */}
                <Button type="submit" className="bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 hover:bg-blue-500 w-full my-5 dark:text-white text-white">
                    <Typography text="Next" variant="p" />
                </Button>
            </form>
        </Form>
    );
};

export default FirstStepper;
