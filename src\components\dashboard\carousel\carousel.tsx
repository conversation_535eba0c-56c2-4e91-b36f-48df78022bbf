"use client"
import { Card, CardContent } from "@/components/ui/card"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"

export function CarouselDemo() {
  return (
    <div className="w-full flex justify-center">
      <div className="relative w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-3xl">
        <Carousel className="w-full overflow-hidden">
          <CarouselContent>
            {Array.from({ length: 5 }).map((_, index) => (
              <CarouselItem key={index} className="w-full sm:w-1/2 md:w-1/3">
                <div className="p-2 sm:p-4">
                  <Card className="shadow-md">
                    <CardContent className="flex aspect-square items-center justify-center p-4 sm:p-6">
                      <span className="text-3xl sm:text-4xl font-semibold">{index + 1}</span>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          {/* Adjusted Button Positioning - Lowered for Better Visibility */}
          <CarouselPrevious className="absolute left-2 top-1/2 translate-y-[30%] flex items-center z-10" />
          <CarouselNext className="absolute right-2 top-1/2 translate-y-[30%] flex items-center z-10" />
        </Carousel>
      </div>
    </div>
  )
}
