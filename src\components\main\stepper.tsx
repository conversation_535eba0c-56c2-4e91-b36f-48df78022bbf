import React from 'react';

interface StepperProps {
    currentStep: number;
    totalSteps: number;
}

const Stepper: React.FC<StepperProps> = ({ currentStep, totalSteps }) => {
    return (
        <div className="items-center justify-between mb-6">
            <div >
                <span className="text-blue-600">{currentStep}</span> / {totalSteps}
            </div>
            <progress
                value={currentStep}
                max={totalSteps}
                className="w-full h-2 rounded-lg bg-gray-200"
            />
        </div>
    );
};

export default Stepper;
