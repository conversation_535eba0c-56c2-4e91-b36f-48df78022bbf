// src/query/case_management.ts
import { supabaseBrowserClient } from "@/supabase/supbaseClient";

export type CaseInsertParams = {
  company_id: string;
  case_title: string;
  case_budget?: number;
  case_description?: string;
  case_expense_board?: string;
  start_date?: string;      
  end_date?: string;         
  status?: string;
  progress?: number;
  priority?: string;
  customer_name?: string;
  customer_can_view?: boolean;
  customer_can_comment?: boolean;
  assigned_to?: string;      
  task_communication?: string;
  phases?: string;
  gantt_charts?: string;
  created_by?: string;       
  updated_by?: string;       
};

export type CaseSummary = {
    id: string;
    case_title: string;
    case_budget: number;
    start_date: string;
    end_date: string;
    status: string;
    case_description: string;   
  };

export async function insertCase(params: CaseInsertParams) {
  const { data, error } = await supabaseBrowserClient
    .from("case_management")
    .insert([params]);

  return { data, error };
}

export async function getCasesByCompany(company_id: string) {
    const { data, error } = await supabaseBrowserClient
      .from("case_management")
      .select("id, case_title, case_budget, start_date, end_date, status, case_description")
      .eq("company_id", company_id);
  
    return { data, error };
}

export async function getCasesByCase(caseId: string) {
    const { data, error } = await supabaseBrowserClient
      .from("case_management")
      .select("*")
      .eq("id", caseId);
  
    return { data, error };
}