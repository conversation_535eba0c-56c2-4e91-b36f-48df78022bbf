"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

type IncomeEntry = {
  id: string;
  source: string;
  date: string;
  amount: number;
};

const incomeData: IncomeEntry[] = [
  { id: "1", source: "Product Sales", date: "2023-10-01", amount: 5000 },
  { id: "2", source: "Service Revenue", date: "2023-10-05", amount: 3000 },
  { id: "3", source: "Subscriptions", date: "2023-10-10", amount: 2000 },
];

export default function IncomePage() {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredIncome = incomeData.filter((income) =>
    income.source.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalIncome = incomeData.reduce((total, income) => total + income.amount, 0);

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center">
            <CardTitle>Income Overview</CardTitle>
            <div className="flex gap-2 mt-4 md:mt-0">
              <Input
                placeholder="Search income..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
              <Button>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Income Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>Total Income</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">${totalIncome}</p>
              </CardContent>
            </Card>
          </div>

          {/* Income Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>No.</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredIncome.map((income, index) => (
                <TableRow key={income.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{income.source}</TableCell>
                  <TableCell>{income.date}</TableCell>
                  <TableCell>${income.amount}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
