"use client"; // Required for client-side interactivity

import { useState } from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Eye, Pencil, Trash, Plus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

type Service = {
  id: number;
  name: string;
  description: string;
  fee: number; // Changed from "price" to "fee" for legal services
  status: "active" | "inactive";
  category: "litigation" | "corporate" | "family" | "real-estate"; // Added legal categories
};

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([
    { id: 1, name: "Civil Litigation", description: "Representation in civil disputes", fee: 5000, status: "active", category: "litigation" },
    { id: 2, name: "Corporate Law", description: "Legal advice for businesses", fee: 10000, status: "active", category: "corporate" },
    { id: 3, name: "Divorce Proceedings", description: "Handling divorce and family law cases", fee: 3000, status: "inactive", category: "family" },
    { id: 4, name: "Real Estate Transactions", description: "Legal support for property deals", fee: 7000, status: "active", category: "real-estate" },
  ]);

  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newService, setNewService] = useState<Omit<Service, "id">>({
    name: "",
    description: "",
    fee: 0,
    status: "active",
    category: "litigation", // Default category
  });

  const handleDelete = (id: number) => {
    setServices((prev) => prev.filter((service) => service.id !== id));
  };

  const handleView = (service: Service) => {
    setSelectedService(service);
    setIsViewModalOpen(true);
  };

  const handleEdit = (service: Service) => {
    setSelectedService(service);
    setIsEditModalOpen(true);
  };

  const handleAdd = () => {
    setIsAddModalOpen(true);
  };

  const handleSaveEdit = () => {
    if (selectedService) {
      setServices((prev) =>
        prev.map((service) =>
          service.id === selectedService.id ? selectedService : service
        )
      );
      setIsEditModalOpen(false);
    }
  };

  const handleSaveNewService = () => {
    const newId = services.length + 1;
    setServices((prev) => [...prev, { ...newService, id: newId }]);
    setIsAddModalOpen(false);
    setNewService({ name: "", description: "", fee: 0, status: "active", category: "litigation" });
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Legal Services</CardTitle>
            <Button onClick={handleAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Service
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Fee</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {services.map((service) => (
                <TableRow key={service.id}>
                  <TableCell>{service.name}</TableCell>
                  <TableCell>{service.description}</TableCell>
                  <TableCell>${service.fee}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{service.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={service.status === "active" ? "default" : "secondary"}>
                      {service.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      {/* View Action */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleView(service)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {/* Edit Action */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(service)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      {/* Delete Action */}
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(service.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>View Service</DialogTitle>
          </DialogHeader>
          {selectedService && (
            <div className="space-y-4">
              <p>
                <span className="font-medium">Name:</span> {selectedService.name}
              </p>
              <p>
                <span className="font-medium">Description:</span>{" "}
                {selectedService.description}
              </p>
              <p>
                <span className="font-medium">Fee:</span> ${selectedService.fee}
              </p>
              <p>
                <span className="font-medium">Category:</span>{" "}
                <Badge variant="outline">{selectedService.category}</Badge>
              </p>
              <p>
                <span className="font-medium">Status:</span>{" "}
                <Badge variant={selectedService.status === "active" ? "default" : "secondary"}>
                  {selectedService.status}
                </Badge>
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Service</DialogTitle>
          </DialogHeader>
          {selectedService && (
            <div className="space-y-4">
              <div>
                <Label>Name</Label>
                <Input
                  value={selectedService.name}
                  onChange={(e) =>
                    setSelectedService({ ...selectedService, name: e.target.value })
                  }
                />
              </div>
              <div>
                <Label>Description</Label>
                <Input
                  value={selectedService.description}
                  onChange={(e) =>
                    setSelectedService({ ...selectedService, description: e.target.value })
                  }
                />
              </div>
              <div>
                <Label>Fee</Label>
                <Input
                  type="number"
                  value={selectedService.fee}
                  onChange={(e) =>
                    setSelectedService({ ...selectedService, fee: Number(e.target.value) })
                  }
                />
              </div>
              <div>
                <Label>Category</Label>
                <Select
                  value={selectedService.category}
                  onValueChange={(value: "litigation" | "corporate" | "family" | "real-estate") =>
                    setSelectedService({ ...selectedService, category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="litigation">Litigation</SelectItem>
                    <SelectItem value="corporate">Corporate</SelectItem>
                    <SelectItem value="family">Family</SelectItem>
                    <SelectItem value="real-estate">Real Estate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Status</Label>
                <Select
                  value={selectedService.status}
                  onValueChange={(value: "active" | "inactive") =>
                    setSelectedService({ ...selectedService, status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveEdit}>Save</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Service</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Name</Label>
              <Input
                value={newService.name}
                onChange={(e) =>
                  setNewService({ ...newService, name: e.target.value })
                }
              />
            </div>
            <div>
              <Label>Description</Label>
              <Input
                value={newService.description}
                onChange={(e) =>
                  setNewService({ ...newService, description: e.target.value })
                }
              />
            </div>
            <div>
              <Label>Fee</Label>
              <Input
                type="number"
                value={newService.fee}
                onChange={(e) =>
                  setNewService({ ...newService, fee: Number(e.target.value) })
                }
              />
            </div>
            <div>
              <Label>Category</Label>
              <Select
                value={newService.category}
                onValueChange={(value: "litigation" | "corporate" | "family" | "real-estate") =>
                  setNewService({ ...newService, category: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="litigation">Litigation</SelectItem>
                  <SelectItem value="corporate">Corporate</SelectItem>
                  <SelectItem value="family">Family</SelectItem>
                  <SelectItem value="real-estate">Real Estate</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Status</Label>
              <Select
                value={newService.status}
                onValueChange={(value: "active" | "inactive") =>
                  setNewService({ ...newService, status: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveNewService}>Save</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}