"use client"; // Required for client-side interactivity

import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, ArrowRightLeft, Users, Clock, TrendingUp, CreditCard } from "lucide-react";
import Link from "next/link";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

export default function TransactionsPage() {
  const recentTransactions = [
    {
      id: "1",
      date: "2023-10-01",
      account: "Cash",
      type: "Income",
      amount: 500,
      status: "Completed",
    },
    {
      id: "2",
      date: "2023-10-05",
      account: "Accounts Receivable",
      type: "Credit",
      amount: 1000,
      status: "Pending",
    },
    {
      id: "3",
      date: "2023-10-10",
      account: "Bank",
      type: "Expense",
      amount: 300,
      status: "Completed",
    },
  ];

  const totalIncome = recentTransactions
    .filter((t) => t.type === "Income")
    .reduce((total, t) => total + t.amount, 0);

  const totalExpenses = recentTransactions
    .filter((t) => t.type === "Expense")
    .reduce((total, t) => total + t.amount, 0);

  const netProfit = totalIncome - totalExpenses;

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Transactions</CardTitle>
            <div className="flex gap-2">
              <Link href="/crm/transactions/view">
                <Button variant="outline">View Transactions</Button>
              </Link>
              <Link href="/crm/transactions/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  New Transaction
                </Button>
              </Link>
              <Link href="/crm/transactions/transfer">
                <Button variant="outline">
                  <ArrowRightLeft className="h-4 w-4 mr-2" />
                  New Transfer
                </Button>
              </Link>
              <Link href="/crm/transactions/clients">
                <Button variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Client Transactions
                </Button>
              </Link>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Dashboard Layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Total Income Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-sm font-medium">Total Income</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">R{totalIncome}</p>
              </CardContent>
            </Card>

            {/* Total Expenses Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                <CreditCard className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">R{totalExpenses}</p>
              </CardContent>
            </Card>

            {/* Net Profit Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
                <Clock className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">R{netProfit}</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Transactions Table */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Account</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>{transaction.account}</TableCell>
                      <TableCell>{transaction.type}</TableCell>
                      <TableCell>R{transaction.amount}</TableCell>
                      <TableCell>{transaction.status}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}