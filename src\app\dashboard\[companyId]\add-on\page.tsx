"use client";

import React, { useState } from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

const availableAddOns = [
  {
    id: "addon1",
    name: "E-commerce Integration",
    description: "Integrate with popular e-commerce platforms to streamline your sales.",
  },
  {
    id: "addon2",
    name: "Shopify Integration",
    description: "Seamlessly connect your Shopify store with Sebenza System.",
  },
  {
    id: "addon3",
    name: "CRM Integration",
    description: "Integrate with leading CRM systems for enhanced client management.",
  },
  {
    id: "addon4",
    name: "Email Marketing Tools",
    description: "Connect with top email marketing platforms to boost your outreach.",
  },
];

export default function AddOnsPage() {
  const [selectedAddOns, setSelectedAddOns] = useState<string[]>([]);

  const toggleAddOn = (id: string) => {
    setSelectedAddOns((prev) =>
      prev.includes(id) ? prev.filter((addon) => addon !== id) : [...prev, id]
    );
  };

  const handleSubmit = () => {
    // In a real app, this data would be sent to your backend for processing.
    alert("Selected Add-Ons: " + selectedAddOns.join(", "));
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Available Integrations */}
      <Card>
        <CardHeader>
          <CardTitle>Adds-on</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Select from the available integrations that can work with the Sebenza system.
          </p>
          <div className="space-y-4">
            {availableAddOns.map((addon) => (
              <div key={addon.id} className="flex items-center justify-between border p-4 rounded">
                <div>
                  <h3 className="font-bold">{addon.name}</h3>
                  <p className="text-sm text-gray-600">{addon.description}</p>
                </div>
                <div>
                  <Checkbox
                    checked={selectedAddOns.includes(addon.id)}
                    onCheckedChange={() => toggleAddOn(addon.id)}
                  />
                  <Label className="ml-2">Select</Label>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6">
            <Button onClick={handleSubmit}>Integrate Selected Add-ons</Button>
          </div>
        </CardContent>
      </Card>

      {/* Future Integrations Information */}
      {/* <Card>
        <CardHeader>
          <CardTitle>Future Integrations</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            In the future, Sebenza System will extend its integrations to additional systems
            such as various e-commerce platforms, Shopify, and many others. Regardless of the integrations,
            users will continue to pay their monthly fee.
          </p>
        </CardContent>
      </Card> */}
    </div>
  );
}
