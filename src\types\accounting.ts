// Accounting types and interfaces
export interface Account {
  id: string;
  code: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  category: string;
  balance: number;
  parentId?: string;
  isActive: boolean;
  description?: string;
}

export interface JournalEntry {
  id: string;
  entryNumber: string;
  date: Date;
  description: string;
  reference?: string;
  totalDebit: number;
  totalCredit: number;
  status: 'draft' | 'posted' | 'reversed';
  createdBy: string;
  createdAt: Date;
  lines: JournalEntryLine[];
}

export interface JournalEntryLine {
  id: string;
  accountId: string;
  accountCode: string;
  accountName: string;
  description: string;
  debitAmount: number;
  creditAmount: number;
}

export interface Payment {
  id: string;
  paymentNumber: string;
  date: Date;
  amount: number;
  currency: string;
  paymentMethod: string;
  payerName: string;
  payerEmail: string;
  description: string;
  invoiceId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  transactionId?: string;
  gateway: string;
  fees: number;
  netAmount: number;
}

export interface BankTransaction {
  id: string;
  date: Date;
  description: string;
  amount: number;
  type: 'debit' | 'credit';
  balance: number;
  reconciled: boolean;
  matchedEntryId?: string;
}

export interface BankReconciliation {
  id: string;
  reconciliationNumber: string;
  bankAccount: string;
  statementDate: Date;
  openingBalance: number;
  closingBalance: number;
  bookBalance: number;
  difference: number;
  status: 'in_progress' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
  bankTransactions: BankTransaction[];
  unmatchedBookEntries: JournalEntry[];
}

export interface FinancialReport {
  id: string;
  name: string;
  type: 'balance_sheet' | 'profit_loss' | 'cash_flow' | 'trial_balance' | 'general_ledger' | 'tax_summary' | 'custom';
  description: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  parameters: {
    accounts: string[];
    includeInactive: boolean;
    groupBy: 'account' | 'category' | 'month' | 'quarter';
    comparison: 'none' | 'previous_period' | 'previous_year';
    format: 'summary' | 'detailed';
  };
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
    recipients: string[];
    nextRun?: Date;
  };
  createdBy: string;
  createdAt: Date;
  lastGenerated?: Date;
  status: 'draft' | 'active' | 'archived';
}

export interface ReportTemplate {
  id: string;
  name: string;
  type: FinancialReport['type'];
  description: string;
  defaultParameters: FinancialReport['parameters'];
  isSystem: boolean;
} 