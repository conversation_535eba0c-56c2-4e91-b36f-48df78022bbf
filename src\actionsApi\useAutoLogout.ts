import { useEffect } from "react";
import { signOut } from "./login";
 

function useAutoLogout() {
  useEffect(() => {
    let timer = setTimeout(() => {
      signOut();
    }, 3600000); 

    const resetTimer = () => {
      clearTimeout(timer);
      timer = setTimeout(() => {
        signOut();
      }, 3600000);
    };

    window.addEventListener("mousemove", resetTimer);
    window.addEventListener("keypress", resetTimer);

    return () => {
      window.removeEventListener("mousemove", resetTimer);
      window.removeEventListener("keypress", resetTimer);
      clearTimeout(timer);
    };
  }, []);
}

export default useAutoLogout;
