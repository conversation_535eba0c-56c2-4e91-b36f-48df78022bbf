"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"; // Import useRouter
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  PlusCircle,
  User,
  FileText,
  CreditCard,
  Briefcase,
  Quote,
  Rocket,
  Calendar,
  StickyNote,
  Folder,
  ArrowLeft,
} from "lucide-react";
import { fetchClientById } from "@/query/clients";
import { toast } from "sonner";

type Client = {
  id: string;
  full_name?: string;
  email?: string;
  phone?: string;
  company?: string;
  billingAddress?: {
    street?: string;
    city?: string;
    country?: string;
  };
  walletBalance?: number;
  income?: number;
  expenses?: number;
  totalDue?: number;
  totalDiscount?: number;
};

export default function ClientProfilePage() {
  const { id } = useParams();
  const router = useRouter(); // Initialize useRouter
  const [client, setClient] = useState<Client | null>(null);

  useEffect(() => {
    async function load() {
      const { data, error } = await fetchClientById(id as string);

      if (error) {
        toast.error(error);
        return;
      }
      if (!data) {
        toast.error("No client data");
        return;
      }

      setClient(data);
    }

    if (id) load();
  }, [id]);

  if (!client) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 border-solid"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Back Button */}
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          onClick={() => router.back()} // Navigate back to the previous page
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            Customer Details: {client?.full_name || "No Name Provided"}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col lg:flex-row gap-8">
          {/* Left Panel */}
          <div className="lg:w-1/3 flex flex-col items-center">
            <div className="w-48 h-48 bg-gray-200 rounded-full mb-4" />
            <div className="text-sm text-muted-foreground mb-4">
              Client Group:{" "}
              <span className="font-semibold">Default Group</span>
            </div>
            <div className="flex flex-col w-full gap-2">
              <Button className="bg-blue-500 hover:bg-blue-600 w-full">
                <User className="w-4 h-4 mr-2" /> View
              </Button>
              <Button className="bg-emerald-400 hover:bg-emerald-500 w-full">
                <FileText className="w-4 h-4 mr-2" /> View Invoices
              </Button>
              <Button className="bg-gray-500 hover:bg-gray-600 w-full">
                <CreditCard className="w-4 h-4 mr-2" /> View Transactions
              </Button>
              <Button className="bg-cyan-500 hover:bg-cyan-600 w-full">
                <Briefcase className="w-4 h-4 mr-2" /> Account Statements
              </Button>
              <Button className="bg-purple-500 hover:bg-purple-600 w-full">
                <Quote className="w-4 h-4 mr-2" /> Quotes
              </Button>
              <Button className="bg-sky-500 hover:bg-sky-600 w-full">
                <Rocket className="w-4 h-4 mr-2" /> Projects
              </Button>
              <Button className="bg-pink-600 hover:bg-pink-700 w-full">
                <Calendar className="w-4 h-4 mr-2" /> Subscriptions
              </Button>
              <Button 
                className="bg-gray-800 hover:bg-gray-900 text-white w-full
                onClick={() => router.push(`/dashboard/${companyId}/meeting-management`)} 
              ">
                <StickyNote className="w-4 h-4 mr-2" /> Meetings
              </Button>
              <Button className="bg-indigo-700 hover:bg-indigo-800 text-white w-full">
                <Folder className="w-4 h-4 mr-2" /> Documents
              </Button>
            </div>
          </div>

          {/* Right Panel */}
          <div className="flex-1 space-y-4">
            {/* Top Buttons */}
            <div className="flex flex-wrap gap-2">
              <Button className="bg-sky-500 hover:bg-sky-600 text-white">
                ✉️ Send Message
              </Button>
              <Button className="bg-cyan-500 hover:bg-cyan-600 text-white">
                🖊️ Edit Profile
              </Button>
              <Button className="bg-rose-500 hover:bg-rose-600 text-white">
                🔑 Change Password
              </Button>
            </div>

            {/* Client Info */}
            <div className="space-y-2 mt-2">
              <div>
                <strong>Company:</strong> {client.company || "No Company Provided"}
              </div>
              <div>
                <strong>Email:</strong> {client.email || "No Email Provided"}
              </div>
              <div>
                <strong>Phone:</strong> {client.phone || "No Phone Provided"}
              </div>
              <div>
                <strong>Address:</strong>{" "}
                {client.billingAddress?.street || "No Street Provided"},{" "}
                {client.billingAddress?.city || "No City Provided"},{" "}
                {client.billingAddress?.country || "No Country Provided"}
              </div>
            </div>

            <Separator className="my-2" />

            {/* Expandable Links */}
            <div className="space-y-4">
              <div className="text-sky-700 flex items-center gap-2 cursor-pointer">
                <PlusCircle className="w-4 h-4" /> ADDRESS
              </div>
              <div className="text-sky-700 flex items-center gap-2 cursor-pointer">
                <PlusCircle className="w-4 h-4" /> SHIPPING ADDRESS
              </div>
              <div className="text-sky-700 flex items-center gap-2 cursor-pointer">
                <PlusCircle className="w-4 h-4" /> EXTRA
              </div>
            </div>

            {/* Wallet Section */}
            <div className="bg-blue-100 text-blue-900 p-4 rounded-md">
              Wallet Balance: {client.walletBalance !== undefined ? client.walletBalance : "0.00"}
            </div>

            {/* Summary */}
            <div className="mt-4 space-y-2">
              <h4 className="font-semibold text-lg">Summary</h4>
              <div className="flex flex-col gap-2">
                <div className="bg-green-100 text-green-800 inline-block px-3 py-1 rounded-md font-medium">
                  {client.income !== undefined ? client.income : "0.00"} Income
                </div>
                <div className="bg-red-100 text-red-800 inline-block px-3 py-1 rounded-md font-medium">
                  {client.expenses !== undefined ? client.expenses : "0.00"} Expenses
                </div>
                <div className="bg-pink-100 text-pink-800 inline-block px-3 py-1 rounded-md font-medium">
                  {client.totalDue !== undefined ? client.totalDue : "0.00"} Total Due
                </div>
                <div className="bg-blue-100 text-blue-800 inline-block px-3 py-1 rounded-md font-medium">
                  {client.totalDiscount !== undefined ? client.totalDiscount : "0.00"} Total Discount
                </div>
              </div>
            </div>

            {/* Wallet History */}
            <div className="pt-6 text-sky-600 font-medium">
              WALLET RECHARGE/PAYMENT HISTORY
            </div>

            {/* Image Upload */}
            <div className="pt-4">
              <label className="font-medium">Change Customer Picture</label>
              <input type="file" className="block mt-2 border rounded-md p-1" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
