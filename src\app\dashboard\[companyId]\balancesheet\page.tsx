/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

export default function BalanceSheetPage() {
  const accounts = [
    { id: "1", name: "Cash", type: "Asset", balance: 50000 },
    { id: "2", name: "Accounts Receivable", type: "Asset", balance: 20000 },
    { id: "3", name: "Office Supplies", type: "Expense", balance: 500 },
    { id: "4", name: "Consulting Revenue", type: "Income", balance: 10000 },
    { id: "5", name: "Accounts Payable", type: "Liability", balance: 15000 },
    { id: "6", name: "Equity", type: "Equity", balance: 45000 },
  ];

  const assets = accounts.filter((account) => account.type === "Asset");
  const expenses = accounts.filter((account) => account.type === "Expense");
  const income = accounts.filter((account) => account.type === "Income");
  const liabilities = accounts.filter((account) => account.type === "Liability");
  const equity = accounts.filter((account) => account.type === "Equity");

  const totalAssets = assets.reduce((total, account) => total + account.balance, 0);
  const totalExpenses = expenses.reduce((total, account) => total + account.balance, 0);
  const totalIncome = income.reduce((total, account) => total + account.balance, 0);
  const totalLiabilities = liabilities.reduce((total, account) => total + account.balance, 0);
  const totalEquity = equity.reduce((total, account) => total + account.balance, 0);

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Balance Sheet</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Assets Section */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-4">Assets</h2>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Account Name</TableHead>
                  <TableHead>Balance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {assets.map((account) => (
                  <TableRow key={account.id}>
                    <TableCell>{account.name}</TableCell>
                    <TableCell>${account.balance}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <div className="flex justify-end mt-4">
              <p className="font-medium">Total Assets: ${totalAssets}</p>
            </div>
          </div>

          {/* Liabilities Section */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-4">Liabilities</h2>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Account Name</TableHead>
                  <TableHead>Balance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {liabilities.map((account) => (
                  <TableRow key={account.id}>
                    <TableCell>{account.name}</TableCell>
                    <TableCell>${account.balance}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <div className="flex justify-end mt-4">
              <p className="font-medium">Total Liabilities: ${totalLiabilities}</p>
            </div>
          </div>

          {/* Equity Section */}
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-4">Equity</h2>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Account Name</TableHead>
                  <TableHead>Balance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {equity.map((account) => (
                  <TableRow key={account.id}>
                    <TableCell>{account.name}</TableCell>
                    <TableCell>${account.balance}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <div className="flex justify-end mt-4">
              <p className="font-medium">Total Equity: ${totalEquity}</p>
            </div>
          </div>

          {/* Totals Section */}
          <div className="mt-6">
            <div className="flex justify-between">
              <p className="font-medium">Total Liabilities and Equity:</p>
              <p>${totalLiabilities + totalEquity}</p>
            </div>
            <div className="flex justify-between">
              <p className="font-medium">Net Assets:</p>
              <p>${totalAssets - totalLiabilities}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}