import { supabaseBrowserClient } from "@/supabase/supbaseClient";

export type EventType = "invoice" | "project" | "task" | "meeting" | "reminder";

export interface CalendarEventInput {
  company_id:       string;       // UUID of the company
  belong_to:        string;       // UUID of the user this event belongs to
  title:            string;
  event_date:       string;       // ISO date, e.g. "2025-04-17"
  type:             EventType;
  description?:     string | null;
  reminder_enabled: boolean;
}

// Exactly matches your table columns (snake_case)
export interface CalendarEventRecord extends CalendarEventInput {
  id:          number;
  created_at:  string;
  created_by:  string;
  updated_at:  string;
  updated_by:  string;
  deleted_at:  string | null;
  deleted_by:  string | null;
}

const supabase = supabaseBrowserClient;

/**
 * Inserts a new calendar_event row and returns it.
 */
export async function createCalendarEvent(
  input: CalendarEventInput & { created_by: string }
) {
  const { data, error } = await supabase
    .from("calendar_event")
    .insert([{
      company_id:       input.company_id,
      belong_to:        input.belong_to,
      title:            input.title,
      event_date:       input.event_date,
      type:             input.type,
      description:      input.description ?? null,
      reminder_enabled: input.reminder_enabled,
      created_by:       input.created_by,
      updated_by:       input.created_by,
    }])
    .select("*")
    .single();

  if (error) return { error };
  return { data };
}

/**
 * Fetches all calendar events for a given company AND user.
 */
export async function fetchCalendarEvents(
  companyId: string,
  userId:    string
) {
  const { data, error } = await supabase
    .from("calendar_event")
    .select("*")
    .eq("company_id",  companyId)
    .eq("belong_to",   userId)
    .order("event_date", { ascending: true });

  if (error) return { error };
  return { data };
}

/**
 * Update an existing event’s fields.
 */
export async function updateCalendarEvent(
  id:      number,
  updates: Partial<Pick<
    CalendarEventRecord,
    | "title"
    | "event_date"
    | "type"
    | "description"
    | "reminder_enabled"
  >> & { updated_by: string }
) {
  const { data, error } = await supabase
    .from("calendar_event")
    .update({
      ...updates,
      updated_by: updates.updated_by,
    })
    .eq("id", id)
    .single();

  if (error) return { error };
  return { data };
}

/**
 * Soft‑delete an event.
 */
export async function deleteCalendarEvent(
  id:        number,
  deleted_by: string
) {
  const { data, error } = await supabase
    .from("calendar_event")
    .update({
      deleted_at: new Date().toISOString(),
      deleted_by,
    })
    .eq("id", id)
    .single();

  if (error) return { error };
  return { data };
}
