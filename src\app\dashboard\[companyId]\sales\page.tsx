"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

type SaleEntry = {
  id: string;
  product: string;
  date: string;
  quantity: number;
  amount: number;
};

const salesData: SaleEntry[] = [
  { id: "1", product: "Product A", date: "2023-10-01", quantity: 3, amount: 300 },
  { id: "2", product: "Product B", date: "2023-10-05", quantity: 2, amount: 200 },
  { id: "3", product: "Product C", date: "2023-10-10", quantity: 5, amount: 500 },
];

export default function Sales() {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredSales = salesData.filter((sale) =>
    sale.product.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalSales = salesData.reduce((total, sale) => total + sale.amount, 0);

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center">
            <CardTitle>Sales Overview</CardTitle>
            <div className="flex gap-2 mt-4 md:mt-0">
              <Input
                placeholder="Search sales..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
              <Button>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Sales Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>Total Sales</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">${totalSales}</p>
              </CardContent>
            </Card>
          </div>
          {/* Sales Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>No.</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSales.map((sale, index) => (
                <TableRow key={sale.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{sale.product}</TableCell>
                  <TableCell>{sale.date}</TableCell>
                  <TableCell>{sale.quantity}</TableCell>
                  <TableCell>${sale.amount}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
