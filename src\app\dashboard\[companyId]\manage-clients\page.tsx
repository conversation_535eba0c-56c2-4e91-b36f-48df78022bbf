/* src/app/dashboard/[companyId]/manage-clients/page.tsx */
"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { Card, CardHeader,  CardContent, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {  Plus, Search } from "lucide-react";
import { fetchClientsByCompanyId } from "@/query/clients";


type Client = {
  id: string;
  full_name?: string;
  email?: string;
  phone?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
};

export default function ManageClientsPage() {
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };

  const [clients, setClients] = useState<Client[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (!companyId) return;
    fetchClientsByCompanyId(companyId).then(res => {
      if (!res.error) {
        setClients(res.data || []);
      } else {
        console.error(res.error);
      }
    });
  }, [companyId]);

  const filteredClients = clients.filter(c =>
    (c.full_name ?? "")
      .toLowerCase()
      .includes(searchTerm.toLowerCase()) ||
    (c.email ?? "")
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  const handleViewProfile = (clientId: string) => {
    router.push(`/dashboard/${companyId}/manage-clients/${clientId}`);
  };

  return (
    <div className="container mx-auto p-4">
      {/* Top header with title + Add Client */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 className="text-2xl font-bold"></h1>
        <Link href={`/dashboard/${companyId}/clients`}>
          <Button className="flex items-center">
            <Plus className="h-4 w-4 mr-2" /> Add Client
          </Button>
        </Link>
      </div>

      <Card className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
        
        
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-center">
            <CardTitle className="mb-4 md:mb-0">Manage Clients</CardTitle>
            <div className="flex gap-2 w-full md:w-1/2">
              <Input
                placeholder="Search name or email..."
                value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
                className="w-full sm:w-64"
              />
              <Button className="flex items-center">
                <Search className="h-4 w-4 mr-2" /> Search
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {filteredClients.length === 0 ? (
            <p className="text-gray-700 dark:text-gray-300">
              {searchTerm ? "No matching clients." : "No clients available."}
            </p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full text-left text-sm">
                <thead className="bg-gray-100 dark:bg-gray-800">
                  <tr>
                    <th className="p-3 font-semibold text-gray-700 dark:text-gray-200">
                      Name
                    </th>
                    <th className="p-3 font-semibold text-gray-700 dark:text-gray-200">
                      Address
                    </th>
                    <th className="p-3 font-semibold text-gray-700 dark:text-gray-200">
                      Email
                    </th>
                    <th className="p-3 font-semibold text-gray-700 dark:text-gray-200">
                      Phone
                    </th>
                    <th className="p-3 font-semibold text-gray-700 dark:text-gray-200">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredClients.map(client => (
                    <tr
                      key={client.id}
                      className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                      <td
                        className="p-3 text-gray-800 dark:text-gray-300 cursor-pointer hover:underline"
                        onClick={() => handleViewProfile(client.id)}
                      >
                        {client.full_name || "No Name Provided"}
                      </td>
                      <td className="p-3 text-gray-600 dark:text-gray-400">
                        {`${client.billing_street || "—"}, ${client.billing_city ||
                          "—"}, ${client.billing_state || "—"}`}
                      </td>
                      <td className="p-3 text-gray-600 dark:text-gray-400">
                        {client.email || "—"}
                      </td>
                      <td className="p-3 text-gray-600 dark:text-gray-400">
                        {client.phone || "—"}
                      </td>
                      <td className="p-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewProfile(client.id)}
                        >
                          View
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
