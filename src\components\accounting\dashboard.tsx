import React from 'react';
// Import all required UI components and icons here
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '../ui/select';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../ui/table';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '../ui/dropdown-menu';
import { Plus, Building2, AlertCircle, TrendingUp, Wallet, BookOpen, BarChart3, CreditCard, CheckCircle, Shuffle, Target, Receipt, FileCheck, Calculator, Activity, Search, MoreHorizontal, Eye, Edit, Printer, Trash2 } from 'lucide-react';

interface DashboardProps {
  metrics: any;
  formatCurrency: (amount: number, currency?: string) => string;
  setShowJournalDialog: (show: boolean) => void;
  setActiveTab: (tab: string) => void;
  setShowPaymentDialog: (show: boolean) => void;
  setShowReconciliationDialog: (show: boolean) => void;
  setShowQuickTransactionDialog: (show: boolean) => void;
  setShowBudgetDialog: (show: boolean) => void;
  setShowExpenseTrackingDialog: (show: boolean) => void;
  setShowInvoicePaymentDialog: (show: boolean) => void;
  setShowTaxCalculatorDialog: (show: boolean) => void;
  setShowAuditTrailDialog: (show: boolean) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  sortBy: string;
  setSortBy: (sort: string) => void;
  sortOrder: string;
  setSortOrder: (order: string) => void;
  journalEntries: any[];
  handleViewJournalDetails: (entry: any) => void;
  setEditingTransaction: (entry: any) => void;
  setJournalForm: (form: any) => void;
  handlePrintJournalEntry: (entry: any) => void;
  handleReverseJournalEntry: (id: string) => void;
  handlePostJournalEntry: (id: string) => void;
  handleDeleteJournalEntry: (id: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({
  metrics,
  formatCurrency,
  setShowJournalDialog,
  setActiveTab,
  setShowPaymentDialog,
  setShowReconciliationDialog,
  setShowQuickTransactionDialog,
  setShowBudgetDialog,
  setShowExpenseTrackingDialog,
  setShowInvoicePaymentDialog,
  setShowTaxCalculatorDialog,
  setShowAuditTrailDialog,
  searchTerm,
  setSearchTerm,
  filterStatus,
  setFilterStatus,
  sortBy,
  setSortBy,
  sortOrder,
  setSortOrder,
  journalEntries,
  handleViewJournalDetails,
  setEditingTransaction,
  setJournalForm,
  handlePrintJournalEntry,
  handleReverseJournalEntry,
  handlePostJournalEntry,
  handleDeleteJournalEntry
}) => {
  return (
    <div className="space-y-6">
      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Building2 size={20} className="text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Assets</p>
                <p className="text-xl font-bold">{formatCurrency(metrics.totalAssets)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle size={20} className="text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Total Liabilities</p>
                <p className="text-xl font-bold">{formatCurrency(metrics.totalLiabilities)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp size={20} className="text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Net Income</p>
                <p className="text-xl font-bold">{formatCurrency(metrics.netIncome)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Wallet size={20} className="text-purple-600" />
    <div>
                <p className="text-sm text-gray-600">Working Capital</p>
                <p className="text-xl font-bold">{formatCurrency(metrics.workingCapital)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowJournalDialog(true)}>
              <BookOpen size={24} />
              <span className="mt-2 text-xs">Journal Entry</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setActiveTab("reports")}> 
              <BarChart3 size={24} />
              <span className="mt-2 text-xs">Financial Reports</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowPaymentDialog(true)}>
              <CreditCard size={24} />
              <span className="mt-2 text-xs">Process Payment</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowReconciliationDialog(true)}>
              <CheckCircle size={24} />
              <span className="mt-2 text-xs">Bank Reconciliation</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowQuickTransactionDialog(true)}>
              <Shuffle size={24} />
              <span className="mt-2 text-xs">Quick Transaction</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowBudgetDialog(true)}>
              <Target size={24} />
              <span className="mt-2 text-xs">Budget Planning</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowExpenseTrackingDialog(true)}>
              <Receipt size={24} />
              <span className="mt-2 text-xs">Expense Tracking</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowInvoicePaymentDialog(true)}>
              <FileCheck size={24} />
              <span className="mt-2 text-xs">Invoice Payment</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowTaxCalculatorDialog(true)}>
              <Calculator size={24} />
              <span className="mt-2 text-xs">Tax Calculator</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col" onClick={() => setShowAuditTrailDialog(true)}>
              <Activity size={24} />
              <span className="mt-2 text-xs">Audit Trail</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity with Enhanced Controls */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Recent Transactions</CardTitle>
              <p className="text-sm text-gray-600">Latest financial entries with full management</p>
            </div>
            <div className="flex gap-2">
              <div className="flex items-center gap-2">
                <Search size={16} className="text-gray-500" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-48"
                />
              </div>
              <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="posted">Posted</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="reversed">Reversed</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => setShowJournalDialog(true)}>
                <Plus size={16} className="mr-2" />
                New Entry
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setSortBy('date');
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Date {sortBy === 'date' && (sortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Entry #</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Account</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setSortBy('amount');
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Amount {sortBy === 'amount' && (sortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setSortBy('status');
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Status {sortBy === 'status' && (sortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {journalEntries
                .filter(entry => {
                  const matchesSearch = entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                      entry.entryNumber.toLowerCase().includes(searchTerm.toLowerCase());
                  const matchesStatus = filterStatus === 'all' || entry.status === filterStatus;
                  return matchesSearch && matchesStatus;
                })
                .sort((a, b) => {
                  switch (sortBy) {
                    case 'date':
                      return sortOrder === 'asc' 
                        ? new Date(a.date).getTime() - new Date(b.date).getTime()
                        : new Date(b.date).getTime() - new Date(a.date).getTime();
                    case 'amount':
                      return sortOrder === 'asc' 
                        ? a.totalDebit - b.totalDebit
                        : b.totalDebit - a.totalDebit;
                    case 'status':
                      return sortOrder === 'asc' 
                        ? a.status.localeCompare(b.status)
                        : b.status.localeCompare(a.status);
                    default:
                      return 0;
                  }
                })
                .slice(0, 10)
                .map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>{entry.date.toLocaleDateString()}</TableCell>
                  <TableCell className="font-medium">{entry.entryNumber}</TableCell>
                  <TableCell>{entry.description}</TableCell>
                  <TableCell>{entry.lines[0]?.accountName}</TableCell>
                  <TableCell>{formatCurrency(entry.totalDebit)}</TableCell>
                  <TableCell>
                    <Badge className={entry.status === 'posted' ? 'bg-green-100 text-green-800' : 
                                    entry.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
                                    'bg-red-100 text-red-800'}>
                      {entry.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal size={14} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => {
                          handleViewJournalDetails(entry);
                        }}>
                          <Eye size={14} className="mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          setEditingTransaction(entry);
                          setJournalForm({
                            date: entry.date,
                            description: entry.description,
                            reference: entry.reference || '',
                            lines: entry.lines.map((line: any) => ({
                              accountId: line.accountId,
                              accountCode: line.accountCode,
                              accountName: line.accountName,
                              description: line.description,
                              debitAmount: line.debitAmount,
                              creditAmount: line.creditAmount
                            }))
                          });
                          setShowJournalDialog(true);
                        }}>
                          <Edit size={14} className="mr-2" />
                          Edit Entry
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          handlePrintJournalEntry(entry);
                        }}>
                          <Printer size={14} className="mr-2" />
                          Print Entry
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {entry.status === 'posted' && (
                          <DropdownMenuItem onClick={() => handleReverseJournalEntry(entry.id)}>
                            <Shuffle size={14} className="mr-2" />
                            Reverse Entry
                          </DropdownMenuItem>
                        )}
                        {entry.status === 'draft' && (
                          <DropdownMenuItem onClick={() => handlePostJournalEntry(entry.id)}>
                            <CheckCircle size={14} className="mr-2" />
                            Post Entry
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          className="text-red-600"
                          onClick={() => {
                            if (confirm('Are you sure you want to delete this journal entry?')) {
                              handleDeleteJournalEntry(entry.id);
                            }
                          }}
                        >
                          <Trash2 size={14} className="mr-2" />
                          Delete Entry
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="flex justify-between items-center mt-4">
            <p className="text-sm text-gray-600">
              Showing {Math.min(10, journalEntries.length)} of {journalEntries.length} entries
            </p>
            <Button variant="outline" onClick={() => setActiveTab("journal")}> 
              View All Entries
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
