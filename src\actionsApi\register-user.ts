'use server';
import { supabaseServerClient } from '@/supabase/supabaseServer';

export async function signUpUser({
    fullName,
    email,
    password,
    phone,
    gender,
    billingStreet,
    billingCity,
    billingState,
    billingZip,
    billingCountry,

}: {
    fullName: string;
    email: string;
    password: string;
    phone: string;
    gender: string;
    billingStreet: string;
    billingCity: string;
    billingState: string;
    billingZip: string;
    billingCountry: string;
}) {
    const supabase = await supabaseServerClient();

    try {
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email,
            password,
            options: {
            data: {
                fullName,
                role: 'CEO',
                phone,
                gender,
                billingStreet,
                billingCity,
                billingState,
                billingZip,
                billingCountry,
            },
            },
        });
  

        if (authError) {
            
            if (authError.status === 400 && authError.message.includes('User already registered')) {
                return { error: 'This email is already registered. Please log in instead.' };
            }

            console.error('Auth error:', authError.message);
            return { error: authError.message };
        }

        const userId = authData.user?.id;

        if (!userId) {
            return { error: 'Failed to retrieve user ID.' };
        }

        return { message: 'User signed up successfully.', userId };
    } catch (error) {
        console.error('Unexpected error during sign-up:', error);
        return { error: 'An unexpected error occurred during sign-up.' };
    }
}
