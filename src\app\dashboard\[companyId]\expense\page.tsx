"use client"; // Required for client-side interactivity

import { useState } from "react";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Search,  Edit, Trash, Plus,  Download } from "lucide-react";

export default function ExpenseManagementPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      client: "Client A",
      case: "Case X",
      description: "Legal Research",
      amount: 500,
      currency: "USD",
      tax: 50,
      billable: true,
      recurring: false,
      receipt: "receipt1.pdf",
      date: "2023-10-01",
    },
    {
      id: 2,
      client: "Client B",
      case: "Case Y",
      description: "Court Fees",
      amount: 300,
      currency: "USD",
      tax: 30,
      billable: false,
      recurring: true,
      receipt: "receipt2.pdf",
      date: "2023-09-25",
    },
  ]);

  const [newExpense, setNewExpense] = useState({
    client: "",
    case: "",
    description: "",
    amount: 0,
    currency: "USD",
    tax: 0,
    billable: true,
    recurring: false,
    receipt: "",
    date: "",
  });

  const handleCreateExpense = () => {
    const newExpenseWithId = { ...newExpense, id: expenses.length + 1 };
    setExpenses(prev => [...prev, newExpenseWithId]);
    setNewExpense({
      client: "",
      case: "",
      description: "",
      amount: 0,
      currency: "USD",
      tax: 0,
      billable: true,
      recurring: false,
      receipt: "",
      date: "",
    });
  };
  

  const handleDeleteExpense = (id: number) => {
    setExpenses((prev) => prev.filter((expense) => expense.id !== id));
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      setNewExpense({
        ...newExpense,
        receipt: e.target.files[0].name,  // <- only the name
      });
    }
  };
  

  const handleExportExpenses = (format: string) => {
    console.log(`Exporting expenses as ${format}`);
    alert(`Expenses exported as ${format}`);
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Expense Management</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search by client or case"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="billable">Billable</SelectItem>
                <SelectItem value="non-billable">Non-Billable</SelectItem>
              </SelectContent>
            </Select>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="ml-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Expense
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Expense</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block font-medium mb-1">Client</label>
                    <Input
                      type="text"
                      value={newExpense.client}
                      onChange={(e) => setNewExpense({ ...newExpense, client: e.target.value })}
                      placeholder="Enter client name"
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Case</label>
                    <Input
                      type="text"
                      value={newExpense.case}
                      onChange={(e) => setNewExpense({ ...newExpense, case: e.target.value })}
                      placeholder="Enter case name"
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Description</label>
                    <Textarea
                      value={newExpense.description}
                      onChange={(e) => setNewExpense({ ...newExpense, description: e.target.value })}
                      placeholder="Enter expense description"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block font-medium mb-1">Amount</label>
                      <Input
                        type="number"
                        value={newExpense.amount}
                        onChange={(e) => setNewExpense({ ...newExpense, amount: Number(e.target.value) })}
                        placeholder="Enter amount"
                      />
                    </div>
                    <div>
                      <label className="block font-medium mb-1">Currency</label>
                      <Select
                        value={newExpense.currency}
                        onValueChange={(value) => setNewExpense({ ...newExpense, currency: value })}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Tax</label>
                    <Input
                      type="number"
                      value={newExpense.tax}
                      onChange={(e) => setNewExpense({ ...newExpense, tax: Number(e.target.value) })}
                      placeholder="Enter tax amount"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="billable"
                      checked={newExpense.billable}
                      onCheckedChange={(checked) => setNewExpense({ ...newExpense, billable: !!checked })}
                    />
                    <label htmlFor="billable" className="text-sm font-medium leading-none">
                      Billable
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="recurring"
                      checked={newExpense.recurring}
                      onCheckedChange={(checked) => setNewExpense({ ...newExpense, recurring: !!checked })}
                    />
                    <label htmlFor="recurring" className="text-sm font-medium leading-none">
                      Recurring
                    </label>
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Date</label>
                    <DatePicker
                      selected={newExpense.date ? new Date(newExpense.date) : null}
                      onChange={(date) => setNewExpense({ ...newExpense, date: date?.toISOString().split('T')[0] || "" })}
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Upload Receipt</label>
                    <Input type="file" onChange={handleFileUpload} />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" onClick={handleCreateExpense}>
                    Create Expense
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Export Options */}
          <div className="flex gap-4 mb-6">
            <Button variant="outline" onClick={() => handleExportExpenses("Excel")}>
              <Download className="mr-2 h-4 w-4" />
              Export as Excel
            </Button>
            <Button variant="outline" onClick={() => handleExportExpenses("PDF")}>
              <Download className="mr-2 h-4 w-4" />
              Export as PDF
            </Button>
            <Button variant="outline" onClick={() => handleExportExpenses("CSV")}>
              <Download className="mr-2 h-4 w-4" />
              Export as CSV
            </Button>
          </div>

          {/* Expenses Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Case</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Currency</TableHead>
                <TableHead>Tax</TableHead>
                <TableHead>Billable</TableHead>
                <TableHead>Recurring</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Receipt</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {expenses.map((expense) => (
                <TableRow key={expense.id}>
                  <TableCell>{expense.client}</TableCell>
                  <TableCell>{expense.case}</TableCell>
                  <TableCell>{expense.description}</TableCell>
                  <TableCell>${expense.amount}</TableCell>
                  <TableCell>{expense.currency}</TableCell>
                  <TableCell>${expense.tax}</TableCell>
                  <TableCell>
                    <Badge variant={expense.billable ? "default" : "secondary"}>
                      {expense.billable ? "Billable" : "Non-Billable"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={expense.recurring ? "default" : "secondary"}>
                      {expense.recurring ? "Recurring" : "One-Time"}
                    </Badge>
                  </TableCell>
                  <TableCell>{expense.date}</TableCell>
                  <TableCell>{expense.receipt}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteExpense(expense.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}