/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { useParams, useRouter } from "next/navigation";
import { useUserProfile } from "@/query/user";
import { toast } from "sonner";
import { User } from "@/types/app";
import { signUpClient, updateClientRelations } from "@/query/clients";

const initialFormData = {
  firstName: "",
  lastName: "",
  email: "",
  password: "",
  phone: "",
  gender: "",
  billingAddress: {
    street: "",
    city: "",
    state: "",
    zip: "",
    country: "",
  },
  additionalDetails: {
    company: "",
    notes: "",
    subscribeToNewsletter: false,
  },
};

export default function CRMClientPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [start, setStarting] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState(initialFormData);
  const [submitting, setSubmitting] = useState(false);
  const params = useParams();
  const companyId = params?.companyId as string;
  const { company, loading } = useUserProfile(currentUser?.id, companyId);
  const router = useRouter();

  
  useEffect(() => {
    fetch("/api/getUser")
      .then((res) => res.json())
      .then((data) => {
        if (data.success) setCurrentUser(data.user.user);
      })
      .catch(console.error);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleBillingAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      billingAddress: { ...prev.billingAddress, [name]: value },
    }));
  };

  const handleAdditionalDetailsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      additionalDetails: { ...prev.additionalDetails, [name]: type === "checkbox" ? checked : value },
    }));
  };

  const clearForm = () => {
    setFormData(initialFormData);
    setStep(1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSubmitting(true);
    const signUpResult = await signUpClient({
      fullName: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      password: formData.password,
    });
    if (signUpResult.error) {
      setError(signUpResult.error);
      toast.error("Signup client failed!");
      setSubmitting(false);
      return;
    }
    if (!signUpResult.clientAuthId) {
      setError("Signup client failed: no clientAuthId returned.");
      toast.error("Signup client failed!");
      setSubmitting(false);
      return;
    }
    const updateResult = await updateClientRelations({
      clientAuthId: signUpResult.clientAuthId,
      companyId,
      supervisorId: currentUser!.id,
      phone: formData.phone,
      gender: formData.gender,
      billingAddress: formData.billingAddress,
      notes: formData.additionalDetails.notes,
      subscribeToNewsletter: formData.additionalDetails.subscribeToNewsletter,
    });
    setSubmitting(false);
    if (updateResult.error) {
      setError(updateResult.error);
      toast.error("Signup client failed!");
    } else {
      toast.success("Signup client successful!");
      clearForm();
      setStep(1);
      router.push(`/dashboard/${companyId}/manage-clients`);

    }
  };

  

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Add New Client</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {step === 1 && (
              <div className="space-y-4">
                <h2 className="text-xl font-bold">Step 1: Personal Information</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>First Name</Label>
                    <Input name="firstName" value={formData.firstName} onChange={handleInputChange} placeholder="John" required />
                  </div>
                  <div>
                    <Label>Last Name</Label>
                    <Input name="lastName" value={formData.lastName} onChange={handleInputChange} placeholder="Doe" required />
                  </div>
                </div>
                <div>
                  <Label>Email</Label>
                  <Input name="email" type="email" value={formData.email} onChange={handleInputChange} placeholder="<EMAIL>" required />
                </div>
                <div>
                  <Label>Password</Label>
                  <Input name="password" type="password" value={formData.password} onChange={handleInputChange} placeholder="Password" required />
                </div>
                <div>
                  <Label>Phone</Label>
                  <Input name="phone" value={formData.phone} onChange={handleInputChange} placeholder="+27652158847" required />
                </div>
                <div>
                  <Label>Gender</Label>
                  <RadioGroup
                    value={formData.gender}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, gender: value }))}
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="male" id="male" />
                      <Label htmlFor="male">Male</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="female" id="female" />
                      <Label htmlFor="female">Female</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="other" id="other" />
                      <Label htmlFor="other">Other</Label>
                    </div>
                  </RadioGroup>
                </div>
                <Button type="button" onClick={() => setStep(2)}>Next</Button>
              </div>
            )}
            {step === 2 && (
              <div className="space-y-4">
                <h2 className="text-xl font-bold">Step 2: Billing Address</h2>
                <div>
                  <Label>Street</Label>
                  <Input name="street" value={formData.billingAddress.street} onChange={handleBillingAddressChange} placeholder="123 Main St" required />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>City</Label>
                    <Input name="city" value={formData.billingAddress.city} onChange={handleBillingAddressChange} placeholder="Johannesburg" required />
                  </div>
                  <div>
                    <Label>State</Label>
                    <Input name="state" value={formData.billingAddress.state} onChange={handleBillingAddressChange} placeholder="" required />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>ZIP Code</Label>
                    <Input name="zip" value={formData.billingAddress.zip} onChange={handleBillingAddressChange} placeholder="10001" required />
                  </div>
                  <div>
                    <Label>Country</Label>
                    <Input name="country" value={formData.billingAddress.country} onChange={handleBillingAddressChange} placeholder="SA" required />
                  </div>
                </div>
                <div className="flex gap-4">
                  <Button type="button" variant="outline" onClick={() => setStep(1)}>Back</Button>
                  <Button type="button" onClick={() => setStep(3)}>Next</Button>
                </div>
              </div>
            )}
            {step === 3 && (
              <div className="space-y-4">
                <h2 className="text-xl font-bold">Step 3: Additional Details</h2>
                <div>
                  <Label>Company</Label>
                  <Input
                    name="company"
                    value={company?.name || formData.additionalDetails.company}
                    onChange={handleAdditionalDetailsChange}
                    placeholder={company?.name || "Company Name"}
                    disabled
                  />
                </div>
                <div>
                  <Label>Notes</Label>
                  <Input name="notes" value={formData.additionalDetails.notes} onChange={handleAdditionalDetailsChange} placeholder="Additional notes..." />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    name="subscribeToNewsletter"
                    checked={formData.additionalDetails.subscribeToNewsletter}
                    onCheckedChange={(checked) =>
                      setFormData((prev) => ({
                        ...prev,
                        additionalDetails: {
                          ...prev.additionalDetails,
                          subscribeToNewsletter: !!checked,
                        },
                      }))
                    }
                  />
                  <Label>Subscribe to Newsletter</Label>
                </div>
                <div className="flex gap-4">
                  <Button type="button" variant="outline" onClick={() => setStep(2)}>Back</Button>
                  <Button type="submit" disabled={submitting}>
                    {submitting ? "Submitting..." : "Submit"}
                  </Button>
                </div>
                {error && <p className="text-red-500">{error}</p>}
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
