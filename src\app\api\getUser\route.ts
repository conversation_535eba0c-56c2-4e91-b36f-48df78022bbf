


import { supabaseServerClient } from "@/supabase/supabaseServer";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const supabase = await supabaseServerClient(); 
    const { data: user, error } = await supabase.auth.getUser();

    if (error) {
      return NextResponse.json({ success: false, error: error.message }, { status: 401 });
    }

    return NextResponse.json({ success: true, user });
  } catch (error) {
    console.error("API Error:", error);
    return NextResponse.json({ success: false, error: "Internal Server Error" }, { status: 500 });
  }
}