import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
 
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'utfs.io',
        pathname: '/**', // Allow all paths from this domain
      },
      {
        protocol: 'https',
        hostname: 'cdn.pixabay.com',
        pathname: '/**', // Allow all paths from this domain
      },
    ],
  },
};

export default nextConfig;
