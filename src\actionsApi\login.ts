'use server';
import { supabaseServerClient } from "@/supabase/supabaseServer";
import { redirect } from "next/navigation";

export async function login({ email, password }: { email: string; password: string }) {
  const supabase = await supabaseServerClient();

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    if (error.status === 400) {
      return JSON.stringify({ error: { message: "Invalid login credentials" } });
    }
    if (error.status === 404) {
      return JSON.stringify({ error: { message: "User does not exist" } });
    }
    return JSON.stringify({ error: { message: "An unexpected error occurred" } });
  }

  return JSON.stringify({ data });
}

export async function signOut() {
  const supabase = await supabaseServerClient();
  await supabase.auth.signOut();
  redirect("/");
}
