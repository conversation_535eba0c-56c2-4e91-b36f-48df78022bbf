"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

import { DatePicker } from "@/components/ui/date-picker";
import { Badge } from "@/components/ui/badge";

import { Search,  Edit, Trash, Plus } from "lucide-react";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

export default function PurchaseOrdersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [purchaseOrders, setPurchaseOrders] = useState([
    {
      id: 1,
      client: "Client A",
      matter: "Matter X",
      orderDate: "2023-10-01",
      dueDate: "2023-10-15",
      totalAmount: 5000,
      status: "pending",
    },
    {
      id: 2,
      client: "Client B",
      matter: "Matter Y",
      orderDate: "2023-09-25",
      dueDate: "2023-10-10",
      totalAmount: 3000,
      status: "approved",
    },
    {
      id: 3,
      client: "Client C",
      matter: "Matter Z",
      orderDate: "2023-10-05",
      dueDate: "2023-10-20",
      totalAmount: 7000,
      status: "rejected",
    },
  ]);

  const [newOrder, setNewOrder] = useState({
    client: "",
    matter: "",
    orderDate: "",
    dueDate: "",
    totalAmount: 0,
    status: "pending",
  });

  const filteredPurchaseOrders = purchaseOrders.filter((order) => {
    const matchesSearch = order.client.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDeleteOrder = (id: number) => {
    setPurchaseOrders((prev) => prev.filter((order) => order.id !== id));
  };

  const handleCreateOrder = () => {
    const newOrderWithId = { ...newOrder, id: purchaseOrders.length + 1 };
    setPurchaseOrders((prev) => [...prev, newOrderWithId]);
    setNewOrder({
      client: "",
      matter: "",
      orderDate: "",
      dueDate: "",
      totalAmount: 0,
      status: "pending",
    });
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Purchase Orders</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search by client or matter"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="ml-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Order
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Purchase Order</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block font-medium mb-1">Client</label>
                    <Input
                      type="text"
                      value={newOrder.client}
                      onChange={(e) => setNewOrder({ ...newOrder, client: e.target.value })}
                      placeholder="Enter client name"
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Matter</label>
                    <Input
                      type="text"
                      value={newOrder.matter}
                      onChange={(e) => setNewOrder({ ...newOrder, matter: e.target.value })}
                      placeholder="Enter matter"
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Order Date</label>
                    <DatePicker
                      selected={newOrder.orderDate ? new Date(newOrder.orderDate) : null}
                      onChange={(date) => setNewOrder({ ...newOrder, orderDate: date?.toISOString().split('T')[0] || "" })}
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Due Date</label>
                    <DatePicker
                      selected={newOrder.dueDate ? new Date(newOrder.dueDate) : null}
                      onChange={(date) => setNewOrder({ ...newOrder, dueDate: date?.toISOString().split('T')[0] || "" })}
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Total Amount</label>
                    <Input
                      type="number"
                      value={newOrder.totalAmount}
                      onChange={(e) => setNewOrder({ ...newOrder, totalAmount: Number(e.target.value) })}
                      placeholder="Enter total amount"
                    />
                  </div>
                  <div>
                    <label className="block font-medium mb-1">Status</label>
                    <Select value={newOrder.status} onValueChange={(value) => setNewOrder({ ...newOrder, status: value })}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="approved">Approved</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" onClick={handleCreateOrder}>
                    Create Order
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Purchase Orders Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Matter</TableHead>
                <TableHead>Order Date</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Total Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPurchaseOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.client}</TableCell>
                  <TableCell>{order.matter}</TableCell>
                  <TableCell>{order.orderDate}</TableCell>
                  <TableCell>{order.dueDate}</TableCell>
                  <TableCell>${order.totalAmount}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        order.status === "approved"
                          ? "default"
                          : order.status === "rejected"
                          ? "destructive"
                          : "secondary"
                      }
                    >
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteOrder(order.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}