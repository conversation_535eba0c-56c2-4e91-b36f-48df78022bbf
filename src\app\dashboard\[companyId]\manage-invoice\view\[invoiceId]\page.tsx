/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Edit, Settings, FileCheck, Printer, Mail, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { getInvoiceById, updateInvoiceStatus, convertQuotationToInvoice } from "@/query/invoices";

interface InvoiceItem {
  id: number;
  item: string;
  description: string;
  quantity: number;
  rate: number;
  tax?: number;
  discount?: number;
}

interface InvoiceData {
  id: string;
  company_id: string;
  reference_number: string;
  invoice_date: string;
  due_date?: string;
  shipping_address: string;
  notes?: string;
  signature?: string;
  discount: number;
  created_at: string;
  updated_at: string;
  tax: number;
  status: string;
  quotation_items: InvoiceItem[];
  custom_columns: {
    rate: string;
    item: string;
    description: string;
    quantity: string;
  };
  payment_schedule: {
    dueDate: string;
    amount: number;
    note: string;
  }[];
  totals: {
    sub_total: number;
    total_tax: number;
    total_after_discount: number;
  };
  client?: {
    full_name: string;
    email: string;
    company_name: string;
  };
  is_converted_to_invoice: boolean;
  // Optional banking details may come through the invoice object:
  bankingDetails?: {
    bank: string;
    branchCode: string;
    accountNumber: string;
    companyName: string;
  };
}

export default function InvoiceDetailsPage() {
  const router = useRouter();
  // Allow either "invoiceId" or "id" from the dynamic route
  const params = useParams();
  const invoiceId = (params as any)?.invoiceId || (params as any)?.id;

  const [invoice, setInvoice] = useState<InvoiceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);
  const [currentStatus, setCurrentStatus] = useState("");
  const [converted, setConverted] = useState(false);

  // Dialog states
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState("");
  const [convertDialogOpen, setConvertDialogOpen] = useState(false);
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  useEffect(() => {
    async function fetchInvoice() {
      if (!invoiceId) {
        setLoading(false);
        return;
      }
      const result = await getInvoiceById(invoiceId as string);
      if (result.error) {
        console.error("Error fetching invoice:", result.error);
      } else {
        setInvoice(result.data);
      }
      setLoading(false);
    }
    fetchInvoice();
  }, [invoiceId]);

  useEffect(() => {
    if (invoice) {
      setCurrentStatus(invoice.status);
      setConverted(invoice.is_converted_to_invoice);
    }
  }, [invoice]);

  if (!hasMounted) return null;
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-sm">Loading invoice details...</p>
      </div>
    );
  }
  if (!invoice) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-sm">No invoice found.</p>
      </div>
    );
  }

  // Extract client and company information (adjust as your API returns them)
  const client = invoice.client || (invoice as any).clients;
  const company = (invoice as any).companies || {};

  // Use invoice_date or created_at, whichever you prefer for display
  const invoiceDate = new Date(invoice.created_at).toLocaleDateString("en-US", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

  const statusClass =
    currentStatus === "approved"
      ? "text-green-600 bg-green-100"
      : currentStatus === "rejected"
      ? "text-red-600 bg-red-100"
      : "text-yellow-600 bg-yellow-100";

  
  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    router.push(`/dashboard/${invoice.company_id}/manage-invoice/edit/${invoice.id}`);
  };

  const openStatusDialog = () => {
    setPendingStatus(currentStatus);
    setStatusDialogOpen(true);
  };

  const confirmStatusChange = async () => {
    const res = await updateInvoiceStatus(invoice.id, pendingStatus);
    if (res.error) {
      toast.error("Error updating status");
    } else {
      setCurrentStatus(pendingStatus);
      setConverted(pendingStatus === "approved");
      toast.success("Status updated successfully.");
    }
    setStatusDialogOpen(false);
  };

  const handleConvertToInvoice = async () => {
    if (converted) {
      toast("This invoice has already been converted.");
    } else {
      setConvertDialogOpen(true);
    }
  };

  const confirmConvertToInvoice = async () => {
    const res = await convertQuotationToInvoice(invoice.id);
    if (res.error) {
      toast.error("Error converting to invoice");
    } else {
      setCurrentStatus("approved");
      setConverted(true);
      toast.success("Invoice converted. Invoice ID: " + (res.data as { id: string }).id);
    }
    setConvertDialogOpen(false);
  };

  const handlePrint = () => {
    setPrintDialogOpen(true);
  };

  const confirmPrint = () => {
    setPrintDialogOpen(false);
    window.print();
  };

  const handleSendEmail = () => {
    setEmailDialogOpen(true);
  };

  const confirmSendEmail = () => {
    setEmailDialogOpen(false);
    toast.success("Invoice email sent.");
  };

  return (
    <div className=" mx-auto p-4 sm:p-6">
      {/* Back and Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
        <button onClick={handleBack} className="flex items-center text-gray-600 hover:text-gray-800 mb-3 sm:mb-0">
          <ArrowLeft className="h-5 w-5 mr-1" />
          <span>Back</span>
        </button>
        <div className="flex flex-wrap gap-3">
          <button onClick={handleEdit} className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded flex items-center text-sm">
            <Edit className="h-4 w-4 mr-1" /> Edit
          </button>
          <button onClick={openStatusDialog} className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded flex items-center text-sm">
            <Settings className="h-4 w-4 mr-1" /> Change Status
          </button>
          <button onClick={handleConvertToInvoice} className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded flex items-center text-sm">
            <FileCheck className="h-4 w-4 mr-1" /> Convert to Invoice
          </button>
          <button onClick={handlePrint} className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded flex items-center text-sm">
            <Printer className="h-4 w-4 mr-1" /> Print
          </button>
          <button onClick={handleSendEmail} className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded flex items-center text-sm">
            <Mail className="h-4 w-4 mr-1" /> Email
          </button>
        </div>
      </div>

      <Card>
        <CardHeader>
          {/* Header Row: Logo left and Invoice Reference on right */}
          <div className="flex justify-between items-center">
            <div>
              <img src="/logo.png" alt="Logo" className="h-16" />
            </div>
            <div className="flex items-center justify-center">
              <h1 className="text-2xl font-bold text-center">
                {company?.name || invoice.bankingDetails?.companyName || "Default Company"}
              </h1>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Bill Invoice</p>
              <p className="text-lg font-bold">Reference: {invoice.reference_number}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Combined Invoice Information Row with 3 Columns */}
          <div className="flex justify-between items-center gap-4 mb-4">
            {/* Bill To Details */}
            <div>
              <h3 className="font-semibold uppercase text-sm text-gray-700 mb-1">Bill To</h3>
              <p className="font-semibold">{client?.full_name || "Client Name"}</p>
              <p>{client?.billing_street  || "Address not provided"}</p>
              <p className="text-sm">{client?.email || "<EMAIL>"}</p>
            </div>
            {/* Company Name (in the middle) */}
            <div>
              <h3 className="font-semibold uppercase text-sm text-gray-700 mb-1">Shipped To</h3>
              <p className="font-semibold">{client?.full_name || "Client Name"}</p>
              <p>{invoice.shipping_address || "Address not provided"}</p>
              <p className="text-sm">{client?.email || "<EMAIL>"}</p>
            </div>
            {/* Invoice Information */}
            <div className="text-right">
              <div className="flex justify-between text-sm gap-2">
                <span className="text-gray-700">Invoice Date: </span>
                <span className="font-semibold"> {invoiceDate}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-700">P.O #:</span>
                <span className="font-semibold">1928</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-700">Due Date:</span>
                <span className="font-semibold">
                {invoice.due_date
                ? new Date(invoice.due_date).toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "numeric",
                  })
                : invoice.payment_schedule?.[0]?.dueDate
                ?? "—"}
                </span>
              </div>
            </div>
          </div>

          {/* Items Table */}
          <div className="overflow-x-auto border border-gray-300 rounded mb-4">
            <table className="min-w-full border-collapse text-sm">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-2 px-3 text-left w-16">{invoice.custom_columns.quantity || "Qty"}</th>
                  <th className="py-2 px-3 text-left">{invoice.custom_columns.item || "Description"}</th>
                  <th className="py-2 px-3 text-right w-24">{invoice.custom_columns.rate || "Unit Price"}</th>
                  <th className="py-2 px-3 text-right w-24">Amount</th>
                </tr>
              </thead>
              <tbody>
                {invoice.quotation_items.map((item, index) => {
                  const amount = item.rate * item.quantity;
                  return (
                    <tr key={item.id} className="border-b last:border-0">
                      <td className="py-2 px-3">{item.quantity}</td>
                      <td className="py-2 px-3">{item.item}</td>
                      <td className="py-2 px-3 text-right">R{item.rate}</td>
                      <td className="py-2 px-3 text-right">R{amount}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Totals Section */}
          <div className="flex justify-end mb-6">
            <div className="w-full md:w-1/2 lg:w-1/3 space-y-2 text-sm">
              <div className="flex justify-between border-b border-gray-200 pb-1">
                <span>Subtotal:</span>
                <span>R{invoice.totals.sub_total}</span>
              </div>
              <div className="flex justify-between border-b border-gray-200 pb-1">
                <span>Tax:</span>
                <span>R{invoice.totals.total_tax}</span>
              </div>
              <div className="flex justify-between border-b border-gray-200 pb-1">
                <span>Discount:</span>
                <span>R{invoice.discount}</span>
              </div>
              <div className="flex justify-between font-semibold text-lg">
                <span>Total:</span>
                <span>R{invoice.totals.total_after_discount}</span>
              </div>
            </div>
          </div>

          {/* Signature & Status */}
          <div className="flex justify-between items-center mt-6 mb-2">
            <div>
              {invoice.signature ? (
                <div className="text-sm">
                  <strong>Signature:</strong> {invoice.signature}
                </div>
              ) : (
                <div className="italic text-gray-500">No signature provided.</div>
              )}
            </div>
            <div>
              <span className={`px-3 py-1 rounded ${statusClass} text-sm`}>Status: {currentStatus}</span>
            </div>
          </div>

          {/* Payment Schedule */}
          {invoice.payment_schedule && invoice.payment_schedule.length > 0 && (
            <div className="mt-4 text-sm">
              <h3 className="font-bold mb-2">Payment Schedule</h3>
              <ul className="list-disc pl-5">
                {invoice.payment_schedule.map((item, index) => (
                  <li key={index}>
                    <strong>Due Date:</strong> {item.dueDate} &mdash; <strong>Amount:</strong> R{item.amount}{" "}
                    {item.note && (<> &mdash; <strong>Note:</strong> {item.note}</>)}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Additional Notes / Terms */}
          <div className="mt-4">
            {invoice.notes && (
              <p className="text-sm mb-3">
                <strong>Additional Notes:</strong> {invoice.notes}
              </p>
            )}
            <hr className="my-3" />
            <p className="text-sm text-gray-600">Payment is due within 15 days.</p>
          </div>

          {/* Banking Information at the End */}
          <div className="bg-gray-50 p-4 rounded mt-4 text-sm">
            <h3 className="text-lg font-bold mb-2">Banking Information</h3>
            <p>
              <strong>Bank:</strong> {invoice.bankingDetails?.bank || "Capitec Bank"}
            </p>
            <p>
              <strong>Branch Code:</strong> {invoice.bankingDetails?.branchCode || "33-436"}
            </p>
            <p>
              <strong>Account Number:</strong> {invoice.bankingDetails?.accountNumber || "********"}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* AlertDialogs */}
      <AlertDialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Status</AlertDialogTitle>
            <AlertDialogDescription>
              Please select a new status:
              <select
                value={pendingStatus}
                onChange={(e) => setPendingStatus(e.target.value)}
                className="border border-gray-300 rounded px-2 py-1 mt-2 block w-full text-sm"
              >
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmStatusChange}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={convertDialogOpen} onOpenChange={setConvertDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Convert to Invoice</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to convert this invoice?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmConvertToInvoice}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={printDialogOpen} onOpenChange={setPrintDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Print Invoice</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to print this invoice?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmPrint}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={emailDialogOpen} onOpenChange={setEmailDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Send Invoice</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to send this invoice by email?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmSendEmail}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
