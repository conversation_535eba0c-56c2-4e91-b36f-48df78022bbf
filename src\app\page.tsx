

import { redirect } from "next/navigation"
import { getUserData } from "@/actionsApi/get-user";


const Home = async () => {
    const userData = await getUserData();
    
    if (!userData) {
        redirect('/auth/login')
    }
    if(userData.role === 'CEO' && userData.company_id === null) {
        redirect('/register-company')
    }
    if(userData.role === 'Employee' && userData.company_id === null){
        redirect('/register-company')
    }
    
    return redirect(`/dashboard/${userData.company_id?.[0]}`)
}

export default Home