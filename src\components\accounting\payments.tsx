import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '../ui/select';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell, TableFooter } from '../ui/table';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '../ui/dropdown-menu';
import { Plus, Search, Settings, MoreHorizontal, Edit, Eye, Receipt, Repeat, CheckCircle, XCircle, Download, CreditCard, Wallet, Building2, Banknote, Clock } from 'lucide-react';

interface PaymentsProps {
  paymentSearchTerm: string;
  setPaymentSearchTerm: (term: string) => void;
  paymentFilterStatus: string;
  setPaymentFilterStatus: (status: string) => void;
  paymentFilterGateway: string;
  setPaymentFilterGateway: (gateway: string) => void;
  paymentSortBy: string;
  setPaymentSortBy: (sort: string) => void;
  paymentSortOrder: string;
  setPaymentSortOrder: (order: string) => void;
  payments: any[];
  selectedPaymentIds: string[];
  setSelectedPaymentIds: (ids: string[]) => void;
  handleViewPaymentDetails: (payment: any) => void;
  handleEditPayment: (payment: any) => void;
  handleSendReceipt: (payment: any) => void;
  handleRefundPayment: (payment: any) => void;
  handleBulkPaymentAction: (action: string, paymentIds: string[]) => void;
  getFilteredPayments: () => any[];
  getPaymentStatusColor: (status: string) => string;
  formatCurrency: (amount: number, currency?: string) => string;
  setShowPaymentDialog: (show: boolean) => void;
  setShowPaymentSettingsDialog: (show: boolean) => void;
  setPayments: (payments: any[]) => void;
  toast: any;
}

const Payments: React.FC<PaymentsProps> = ({
  paymentSearchTerm,
  setPaymentSearchTerm,
  paymentFilterStatus,
  setPaymentFilterStatus,
  paymentFilterGateway,
  setPaymentFilterGateway,
  paymentSortBy,
  setPaymentSortBy,
  paymentSortOrder,
  setPaymentSortOrder,
  payments,
  selectedPaymentIds,
  setSelectedPaymentIds,
  handleViewPaymentDetails,
  handleEditPayment,
  handleSendReceipt,
  handleRefundPayment,
  handleBulkPaymentAction,
  getFilteredPayments,
  getPaymentStatusColor,
  formatCurrency,
  setShowPaymentDialog,
  setShowPaymentSettingsDialog,
  setPayments,
  toast
}) => {
  return (
    <>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Payments</h2>
          <p className="text-sm text-gray-600">Process and manage all payment transactions</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowPaymentDialog(true)}>
            <Plus size={16} className="mr-2" />
            Process Payment
          </Button>
          <Button variant="outline" onClick={() => setShowPaymentSettingsDialog(true)}>
            <Settings size={16} className="mr-2" />
            Payment Settings
          </Button>
        </div>
      </div>

      {/* Search, Filter & Actions */}
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <div className="flex gap-4 items-center">
            <div className="flex items-center gap-2">
              <Search size={16} className="text-gray-500" />
              <Input
                placeholder="Search payments..."
                value={paymentSearchTerm}
                onChange={(e) => setPaymentSearchTerm(e.target.value)}
                className="w-80"
              />
            </div>
            <Select value={paymentFilterStatus} onValueChange={setPaymentFilterStatus}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>
            <Select value={paymentFilterGateway} onValueChange={setPaymentFilterGateway}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by gateway" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Gateways</SelectItem>
                <SelectItem value="stripe">Stripe</SelectItem>
                <SelectItem value="paypal">PayPal</SelectItem>
                <SelectItem value="bank">Bank Transfer</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Select value={paymentSortBy} onValueChange={setPaymentSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
                <SelectItem value="payer">Payer</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPaymentSortOrder(paymentSortOrder === 'asc' ? 'desc' : 'asc')}
            >
              <Download size={14} />
            </Button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedPaymentIds.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <span className="text-sm font-medium text-blue-900">
              {selectedPaymentIds.length} payment(s) selected
            </span>
            <Button
              size="sm"
              onClick={() => handleBulkPaymentAction('complete', selectedPaymentIds)}
              disabled={selectedPaymentIds.length === 0}
            >
              <CheckCircle size={14} className="mr-1" />
              Complete
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkPaymentAction('cancel', selectedPaymentIds)}
              disabled={selectedPaymentIds.length === 0}
            >
              <XCircle size={14} className="mr-1" />
              Cancel
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkPaymentAction('export', selectedPaymentIds)}
              disabled={selectedPaymentIds.length === 0}
            >
              <Download size={14} className="mr-1" />
              Export
            </Button>
          </div>
        )}
      </div>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CreditCard size={20} className="text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Payments</p>
                <p className="text-xl font-bold">{formatCurrency(payments.reduce((sum, p) => sum + p.amount, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle size={20} className="text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-xl font-bold">{payments.filter(p => p.status === 'completed').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock size={20} className="text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-xl font-bold">{payments.filter(p => p.status === 'pending').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Banknote size={20} className="text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Processing Fees</p>
                <p className="text-xl font-bold">{formatCurrency(payments.reduce((sum, p) => sum + p.fees, 0))}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Online Payment Gateway Setup */}
      <Card>
        <CardHeader>
          <CardTitle>Accept Online Payments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-2 border-dashed border-blue-300 bg-blue-50">
              <CardContent className="p-4 text-center">
                <CreditCard size={32} className="mx-auto mb-2 text-blue-600" />
                <h4 className="font-semibold">Stripe</h4>
                <p className="text-sm text-gray-600 mb-4">Accept credit cards, bank transfers</p>
                <Button variant="outline" size="sm">Configure</Button>
              </CardContent>
            </Card>
            <Card className="border-2 border-dashed border-green-300 bg-green-50">
              <CardContent className="p-4 text-center">
                <Wallet size={32} className="mx-auto mb-2 text-green-600" />
                <h4 className="font-semibold">PayPal</h4>
                <p className="text-sm text-gray-600 mb-4">PayPal & digital wallets</p>
                <Button variant="outline" size="sm">Configure</Button>
              </CardContent>
            </Card>
            <Card className="border-2 border-dashed border-purple-300 bg-purple-50">
              <CardContent className="p-4 text-center">
                <Building2 size={32} className="mx-auto mb-2 text-purple-600" />
                <h4 className="font-semibold">Bank Transfer</h4>
                <p className="text-sm text-gray-600 mb-4">Direct bank payments</p>
                <Button variant="outline" size="sm">Configure</Button>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Payment History ({getFilteredPayments().length} of {payments.length})</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const csvContent = [
                    ['Payment #', 'Date', 'Payer', 'Amount', 'Fees', 'Net Amount', 'Method', 'Gateway', 'Status'],
                    ...getFilteredPayments().map(p => [
                      p.paymentNumber,
                      p.date.toLocaleDateString(),
                      p.payerName,
                      p.amount,
                      p.fees,
                      p.netAmount,
                      p.paymentMethod,
                      p.gateway,
                      p.status
                    ])
                  ].map(row => row.join(',')).join('\n');
                  
                  const blob = new Blob([csvContent], { type: 'text/csv' });
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `payments-${new Date().toISOString().split('T')[0]}.csv`;
                  a.click();
                  window.URL.revokeObjectURL(url);
                }}
              >
                <Download size={16} className="mr-2" />
                Export CSV
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Payment #</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Payer</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Fees</TableHead>
                <TableHead>Net Amount</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Gateway</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {getFilteredPayments().map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell className="font-medium">{payment.paymentNumber}</TableCell>
                  <TableCell>{payment.date.toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{payment.payerName}</div>
                      <div className="text-sm text-gray-500">{payment.payerEmail}</div>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">{formatCurrency(payment.amount, payment.currency)}</TableCell>
                  <TableCell className="text-red-600">{formatCurrency(payment.fees, payment.currency)}</TableCell>
                  <TableCell className="font-medium text-green-600">{formatCurrency(payment.netAmount, payment.currency)}</TableCell>
                  <TableCell className="capitalize">{payment.paymentMethod.replace('_', ' ')}</TableCell>
                  <TableCell className="capitalize">{payment.gateway}</TableCell>
                  <TableCell>
                    <Badge className={getPaymentStatusColor(payment.status)}>
                      {payment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal size={14} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleViewPaymentDetails(payment)}>
                          <Eye size={14} className="mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleEditPayment(payment)}
                          disabled={payment.status === 'completed' || payment.status === 'refunded'}
                        >
                          <Edit size={14} className="mr-2" />
                          Edit Payment
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleSendReceipt(payment)}>
                          <Receipt size={14} className="mr-2" />
                          Send Receipt
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleRefundPayment(payment)}
                          disabled={payment.status !== 'completed'}
                        >
                          <Repeat size={14} className="mr-2" />
                          Refund
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => {
                            if (payment.status === 'pending') {
                              const updatedPayments = payments.map(p => 
                                p.id === payment.id ? { ...p, status: 'completed' as const } : p
                              );
                              setPayments(updatedPayments);
                              toast({
                                title: "Success",
                                description: "Payment marked as completed!",
                              });
                            }
                          }}
                          disabled={payment.status !== 'pending'}
                        >
                          <CheckCircle size={14} className="mr-2" />
                          Mark Complete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {getFilteredPayments().length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No payments found matching your criteria.
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
};

export default Payments;
