/* eslint-disable @typescript-eslint/no-explicit-any */
import { supabaseBrowserClient } from "@/supabase/supbaseClient";
import { Company, User } from "@/types/app";
import { useState, useEffect } from "react";


export const useUserProfile = (userId: string | undefined, companyId: string | undefined) => {
    const [profile, setProfile] = useState<User | null>(null);
    const [company, setCompany] = useState<Company | null>(null); 
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!userId || !companyId) {
            setError("User ID and Company ID are required");
            setLoading(false);
            return;
        }

        const fetchProfileAndCompany = async () => {
            setLoading(true);
            setError(null);

            try {
                // Fetch user profile
                const { data: userProfile, error: profileError } = await supabaseBrowserClient
                    .from("users")
                    .select("*")
                    .eq("id", userId)
                    .single();

                if (profileError) throw new Error(profileError.message);
                setProfile(userProfile);

                // Fetch company data only when companyId is available
                const { data: companyData, error: companyError } = await supabaseBrowserClient
                    .from("companies")
                    .select("*")
                    .eq("id", companyId)
                    .single();

                if (companyError && companyError.message !== "No rows found") throw new Error(companyError.message);
                setCompany(companyData || null);
            } catch (err: any) {
                console.error("Error fetching data:", err.message);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchProfileAndCompany();
    }, [userId, companyId]); 

    return { profile, company, loading, error };
};
