"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";

import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

export default function SupportTicketsPage() {
  const [department, setDepartment] = useState("Admin");
  const [message, setMessage] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement the submit logic here (e.g., sending to backend)
    alert(`Ticket submitted to ${department} department:\n\n${message}`);
    setMessage("");
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Support Tickets</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label className="block mb-2 font-medium">Select Department</Label>
              <RadioGroup
                value={department}
                onValueChange={setDepartment}
                className="flex flex-wrap gap-4"
              >
                {[
                  "Admin",
                  "Finance",
                  "Supports",
                  "Sales",
                  "General Enquiry",
                  "Complaints",
                  "Suggestion",
                  "Feedback",
                ].map((dept) => (
                  <div key={dept} className="flex items-center space-x-2">
                    <RadioGroupItem value={dept} id={dept} />
                    <Label htmlFor={dept}>{dept}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
            <div>
              <Label className="block mb-2 font-medium">Message</Label>
              <Textarea
                rows={5}
                placeholder="Type your support request message here..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>
            <Button type="submit">Submit Ticket</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
