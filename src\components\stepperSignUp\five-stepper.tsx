"use client";
import { useSignupValues } from '@/hooks/signup-values';
import { Button } from '@/components/ui/button';
import Typography from '@/components/ui/typography';
import { FaDollarSign, FaRocket } from 'react-icons/fa';

const FiveStepper = () => {
    const { setCurrentStep, updateFormData } = useSignupValues();

    const handleOptionSelect = (option: "buyNow" | "startTrial") => {
        updateFormData({ subscriptionOption: option });
        setCurrentStep(6);
        
    };

    const handleBack = () => {
        setCurrentStep(4);
    };

    return (
        <div className="w-full">
            <div className="text-center mb-8">
                <Typography
                    text="Step 5: Subscription Options"
                    variant="h2"
                    className="text-2xl font-bold text-blue-600 dark:text-blue-400"
                />
                <Typography
                    text="Choose your subscription option. You can either start with a 14-day free trial or pay now to access the full features immediately."
                    variant="p"
                    className="opacity-90 mb-7 text-gray-600 dark:text-gray-400 text-center"
                />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
                {/* Buy Now Option */}
                <div className="bg-gradient-to-r from-green-400 to-green-600 text-white rounded-lg shadow-lg p-6 hover:scale-105 transition-transform">
                    <div className="flex items-center mb-4">
                        <FaDollarSign className="text-3xl text-white mr-3" />
                        <h3 className="text-xl font-semibold">Buy Now</h3>
                    </div>
                    <p className="text-sm mb-6">
                        Access all features immediately by purchasing the full package.
                    </p>
                    <Button
                        onClick={() => handleOptionSelect("buyNow")}
                        className="bg-white text-green-600 hover:bg-green-100 px-6 py-3 rounded-lg w-full"
                    >
                        Buy Now
                    </Button>
                </div>

                {/* Start Trial Option */}
                <div className="bg-gradient-to-r from-blue-400 to-blue-600 text-white rounded-lg shadow-lg p-6 hover:scale-105 transition-transform">
                    <div className="flex items-center mb-4">
                        <FaRocket className="text-3xl text-white mr-3" />
                        <h3 className="text-xl font-semibold">Start Trial</h3>
                    </div>
                    <p className="text-sm mb-6">
                        Enjoy all of our best features for free during a 14-day trial period.
                    </p>
                    <Button
                        onClick={() => handleOptionSelect("startTrial")}
                        className="bg-white text-blue-600 hover:bg-blue-100 px-6 py-3 rounded-lg w-full"
                    >
                        Start Trial
                    </Button>
                </div>
            </div>
            <div className="flex justify-between mt-10">
                <Button onClick={handleBack} className="bg-gray-400 hover:bg-gray-300 text-white px-4 py-2 rounded-lg">
                    Back
                </Button>
            </div>
        </div>
    );
};

export default FiveStepper;
