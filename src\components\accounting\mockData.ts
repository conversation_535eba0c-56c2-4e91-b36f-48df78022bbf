// Mock data for accounting components
import { Account, JournalEntry, BankTransaction, BankReconciliation, FinancialReport, ReportTemplate, Payment } from '@/types/accounting';

export const mockAccounts: Account[] = [
  { id: '1', code: '1000', name: 'Cash and Cash Equivalents', type: 'asset', category: 'Current Assets', balance: 85000, isActive: true },
  { id: '2', code: '1100', name: 'Accounts Receivable', type: 'asset', category: 'Current Assets', balance: 45000, isActive: true },
  { id: '3', code: '1200', name: 'Office Equipment', type: 'asset', category: 'Fixed Assets', balance: 25000, isActive: true },
  { id: '4', code: '2000', name: 'Accounts Payable', type: 'liability', category: 'Current Liabilities', balance: 15000, isActive: true },
  { id: '5', code: '3000', name: 'Retained Earnings', type: 'equity', category: 'Equity', balance: 75000, isActive: true },
  { id: '6', code: '4000', name: 'Legal Service Revenue', type: 'revenue', category: 'Operating Revenue', balance: 125000, isActive: true },
  { id: '7', code: '5000', name: 'Office Rent', type: 'expense', category: 'Operating Expenses', balance: 24000, isActive: true },
  { id: '8', code: '5100', name: 'Professional Development', type: 'expense', category: 'Operating Expenses', balance: 8000, isActive: true }
];

export const mockJournalEntries: JournalEntry[] = [
  {
    id: '1',
    entryNumber: 'JE-2024-001',
    date: new Date('2024-01-15'),
    description: 'Client payment received',
    reference: 'INV-001',
    totalDebit: 5000,
    totalCredit: 5000,
    status: 'posted',
    createdBy: 'John Doe',
    createdAt: new Date(),
    lines: [
      { id: '1', accountId: '1', accountCode: '1000', accountName: 'Cash', description: 'Payment received', debitAmount: 5000, creditAmount: 0 },
      { id: '2', accountId: '2', accountCode: '1100', accountName: 'Accounts Receivable', description: 'Invoice payment', debitAmount: 0, creditAmount: 5000 }
    ]
  }
];

export const mockBankTransactions: BankTransaction[] = [
  {
    id: '1',
    date: new Date('2024-01-15'),
    description: 'Deposit - ABC Corp',
    amount: 2500,
    type: 'credit',
    balance: 87500,
    reconciled: false
  },
  {
    id: '2',
    date: new Date('2024-01-14'),
    description: 'Service Fee',
    amount: 25,
    type: 'debit',
    balance: 85000,
    reconciled: false
  },
  {
    id: '3',
    date: new Date('2024-01-13'),
    description: 'Wire Transfer Out',
    amount: 1000,
    type: 'debit',
    balance: 85025,
    reconciled: false
  }
];

export const mockReconciliations: BankReconciliation[] = [
  {
    id: '1',
    reconciliationNumber: 'REC-2024-001',
    bankAccount: 'Cash and Cash Equivalents',
    statementDate: new Date('2024-01-31'),
    openingBalance: 85000,
    closingBalance: 87500,
    bookBalance: 85000,
    difference: 2500,
    status: 'in_progress',
    createdBy: 'John Doe',
    createdAt: new Date(),
    bankTransactions: mockBankTransactions,
    unmatchedBookEntries: []
  }
];

export const mockReports: FinancialReport[] = [
  {
    id: '1',
    name: 'Monthly Balance Sheet',
    type: 'balance_sheet',
    description: 'Comprehensive balance sheet for monthly review',
    dateRange: {
      from: new Date('2024-01-01'),
      to: new Date('2024-01-31')
    },
    parameters: {
      accounts: ['1', '2', '3', '4', '5'],
      includeInactive: false,
      groupBy: 'category',
      comparison: 'previous_period',
      format: 'detailed'
    },
    schedule: {
      enabled: true,
      frequency: 'monthly',
      recipients: ['<EMAIL>', '<EMAIL>'],
      nextRun: new Date('2024-02-01')
    },
    createdBy: 'John Doe',
    createdAt: new Date('2024-01-01'),
    lastGenerated: new Date('2024-01-31'),
    status: 'active'
  },
  {
    id: '2',
    name: 'Profit & Loss Statement',
    type: 'profit_loss',
    description: 'Monthly profit and loss analysis',
    dateRange: {
      from: new Date('2024-01-01'),
      to: new Date('2024-01-31')
    },
    parameters: {
      accounts: ['6', '7', '8'],
      includeInactive: false,
      groupBy: 'account',
      comparison: 'previous_year',
      format: 'summary'
    },
    schedule: {
      enabled: true,
      frequency: 'monthly',
      recipients: ['<EMAIL>'],
      nextRun: new Date('2024-02-01')
    },
    createdBy: 'Jane Smith',
    createdAt: new Date('2024-01-01'),
    lastGenerated: new Date('2024-01-31'),
    status: 'active'
  },
  {
    id: '3',
    name: 'Cash Flow Analysis',
    type: 'cash_flow',
    description: 'Weekly cash flow monitoring',
    dateRange: {
      from: new Date('2024-01-22'),
      to: new Date('2024-01-28')
    },
    parameters: {
      accounts: ['1'],
      includeInactive: false,
      groupBy: 'month',
      comparison: 'none',
      format: 'detailed'
    },
    createdBy: 'John Doe',
    createdAt: new Date('2024-01-22'),
    lastGenerated: new Date('2024-01-28'),
    status: 'draft'
  }
];

export const mockReportTemplates: ReportTemplate[] = [
  {
    id: '1',
    name: 'Standard Balance Sheet',
    type: 'balance_sheet',
    description: 'Standard balance sheet template with all asset, liability, and equity accounts',
    defaultParameters: {
      accounts: [],
      includeInactive: false,
      groupBy: 'category',
      comparison: 'none',
      format: 'detailed'
    },
    isSystem: true
  },
  {
    id: '2',
    name: 'Executive P&L Summary',
    type: 'profit_loss',
    description: 'High-level profit and loss summary for executive reporting',
    defaultParameters: {
      accounts: [],
      includeInactive: false,
      groupBy: 'category',
      comparison: 'previous_period',
      format: 'summary'
    },
    isSystem: true
  },
  {
    id: '3',
    name: 'Cash Flow Forecast',
    type: 'cash_flow',
    description: 'Detailed cash flow analysis with forecasting',
    defaultParameters: {
      accounts: [],
      includeInactive: false,
      groupBy: 'month',
      comparison: 'previous_year',
      format: 'detailed'
    },
    isSystem: false
  }
];

export const mockPayments: Payment[] = [
  {
    id: '1',
    paymentNumber: 'PAY-2024-001',
    date: new Date('2024-01-15'),
    amount: 2500,
    currency: 'ZAR',
    paymentMethod: 'Credit Card',
    payerName: 'ABC Corp',
    payerEmail: '<EMAIL>',
    description: 'Legal consultation fee',
    invoiceId: 'INV-001',
    status: 'completed',
    transactionId: 'txn_abc123',
    gateway: 'Stripe',
    fees: 75,
    netAmount: 2425
  }
]; 