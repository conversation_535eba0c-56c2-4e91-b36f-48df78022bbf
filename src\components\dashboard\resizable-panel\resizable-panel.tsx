/* eslint-disable @typescript-eslint/no-unused-vars */
"use client"
import React from "react"
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable"

import { CarouselDemo } from "../carousel/carousel"
import { TableDemo } from "../table/table"

export function ResizablePanelDemo() {
    return (
        <ResizablePanelGroup
          direction="horizontal"
          className="max-w-full rounded-lg border border-gray-300 dark:border-gray-700 md:min-w-[600px]"
        >
          {/* Table on the left side */}
          <ResizablePanel defaultSize={40} className="p-4">
            <TableDemo />
          </ResizablePanel>
    
          {/* Improved Resizable Handle (Clear in Light & Dark Mode) */}
          <ResizableHandle className="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 w-2 cursor-col-resize" />
    
          {/* Right side resizable panels */}
          <ResizablePanel defaultSize={60}>
            <ResizablePanelGroup direction="vertical">
              <ResizablePanel defaultSize={25}>
                <div className="flex h-full items-center justify-center p-6">
                  <span className="font-semibold">Two</span>
                </div>
              </ResizablePanel>

              {/* Improved Resizable Handle (Vertical) */}
              <ResizableHandle className="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 h-2 cursor-row-resize" />

              <ResizablePanel defaultSize={75}>
                <div className="flex h-full items-center justify-center p-6">
                  <span className="font-semibold">Three</span>
                </div>
              </ResizablePanel>
            </ResizablePanelGroup>
          </ResizablePanel>
        </ResizablePanelGroup>
      )
}
