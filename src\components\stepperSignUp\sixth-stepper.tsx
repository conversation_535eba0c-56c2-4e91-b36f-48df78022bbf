"use client";

import { useSignupValues } from '@/hooks/signup-values';
import { Button } from '@/components/ui/button';
import Typography from '@/components/ui/typography';
import { useRouter } from 'next/navigation';
import { registerCompany } from '@/actionsApi/register-company';
import { toast } from 'sonner';
import { useState } from 'react';

const SixthStepper = ({ userId: ceoId }: { userId: string }) => {
    const { formData } = useSignupValues();
    const [isAuthenticating, setIsAuthenticating] = useState(false);
    const router = useRouter();

    const handleGoToDashboard = async () => {
        setIsAuthenticating(true);

        try {
            const companyData = {
                name: formData.companyName,
                industry: formData.industry,
                country: formData.country,
                city: formData.city,
                address: formData.address,
                currency: formData.currency,
                yearsInBusiness: formData.yearsInBusiness,
                subscriptionOption: formData.subscriptionOption,
                numberOfUsers: formData.numberOfUsers,
                cellNumber: formData.cellNumber,
                language: formData.language,
                skip: formData.skip,
            };

            const employeeIds = formData.employeesIds || []; 

            
            console.log('FormData from SixthStepper:', formData);
            console.log('FormData from SixthStepper one:', formData.employeesIds);
            console.log('Employee IDs to be passed:', employeeIds);

            
            const companyResponse = await registerCompany({ ceoId, companyData, employeedIds: employeeIds });

            if (companyResponse.error) {
                toast.error(`Error registering company: ${companyResponse.error}`);
                setIsAuthenticating(false);
                return;
            }

            
            const companyId = companyResponse.companyId;
            if (!companyId) {
                toast.error('Company ID not returned. Please try again.');
                setIsAuthenticating(false);
                return;
            }

            toast.success('Company registered successfully!');
            
            
            router.push(`/dashboard/${companyId}`);
        } catch (error) {
            console.error('Error during registration:', error);
            toast.error('An unexpected error occurred. Please try again.');
        } finally {
            setIsAuthenticating(false);
        }
    };

    return (
        <div className="flex flex-col items-center text-center w-full">
            <div className="mb-6">
                <Typography
                    text="🎉 Welcome to Sebenza System!"
                    variant="h1"
                    className="text-3xl font-bold text-blue-600 dark:text-blue-400"
                />
                <Typography
                    text="Your Business Management Software"
                    variant="h2"
                    className="text-xl font-medium text-gray-700 dark:text-gray-300 mt-2"
                />
            </div>
            <Button
                onClick={handleGoToDashboard}
                className="bg-blue-600 hover:bg-blue-500 text-white w-full my-5"
                disabled={isAuthenticating}
            >
                {isAuthenticating ? 'Registering...' : 'Go to Dashboard'}
            </Button>
        </div>
    );
};

export default SixthStepper;
