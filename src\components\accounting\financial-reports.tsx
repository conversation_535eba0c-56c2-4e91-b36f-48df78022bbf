import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '../ui/select';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell, TableFooter } from '../ui/table';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '../ui/dropdown-menu';
import { ArrowUpDown, MoreHorizontal, Eye, Edit, FileText, Copy, Clock, CheckCircle, Archive, Download, Trash2 } from 'lucide-react';
import {Checkbox} from '../ui/checkbox';

interface FinancialReportsProps {
  reportSearchTerm: string;
  setReportSearchTerm: (term: string) => void;
  reportFilterType: string;
  setReportFilterType: (type: string) => void;
  reportFilterStatus: string;
  setReportFilterStatus: (status: string) => void;
  reportSortOrder: string;
  setReportSortOrder: (order: string) => void;
  reportSortBy: string;
  setReportSortBy: (sort: string) => void;
  selectAllReports: boolean;
  setSelectAllReports: (checked: boolean) => void;
  getFilteredReports: () => any[];
  setSelectedReportIds: (ids: string[]) => void;
  selectedReportIds: string[];
  handleViewReportDetails: (report: any) => void;
  handleGenerateReport: (report: any) => void;
  handleEditReport: (report: any) => void;
  handleDuplicateReport: (report: any) => void;
  handleScheduleReport: (report: any) => void;
  handleActivateReport: (id: string) => void;
  handleArchiveReport: (id: string) => void;
  handleExportReport: (report: any, format: string) => void;
  handleDeleteReport: (id: string) => void;
}

const FinancialReports: React.FC<FinancialReportsProps> = ({
  reportSearchTerm,
  setReportSearchTerm,
  reportFilterType,
  setReportFilterType,
  reportFilterStatus,
  setReportFilterStatus,
  reportSortOrder,
  setReportSortOrder,
  reportSortBy,
  setReportSortBy,
  selectAllReports,
  setSelectAllReports,
  getFilteredReports,
  setSelectedReportIds,
  selectedReportIds,
  handleViewReportDetails,
  handleGenerateReport,
  handleEditReport,
  handleDuplicateReport,
  handleScheduleReport,
  handleActivateReport,
  handleArchiveReport,
  handleExportReport,
  handleDeleteReport
}) => {
  return (
    <Card>
      <CardContent>
        {/* Enhanced Search, Filter & Sort Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <div className="relative">
              <Input
                placeholder="Search reports by name, description, or creator..."
                value={reportSearchTerm}
                onChange={(e) => setReportSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Select value={reportFilterType} onValueChange={setReportFilterType}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="balance_sheet">Balance Sheet</SelectItem>
                <SelectItem value="profit_loss">Profit & Loss</SelectItem>
                <SelectItem value="cash_flow">Cash Flow</SelectItem>
                <SelectItem value="trial_balance">Trial Balance</SelectItem>
                <SelectItem value="general_ledger">General Ledger</SelectItem>
                <SelectItem value="tax_summary">Tax Summary</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
            <Select value={reportFilterStatus} onValueChange={setReportFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setReportSortOrder(reportSortOrder === 'asc' ? 'desc' : 'asc')}
            >
              <ArrowUpDown size={16} className="mr-2" />
              {reportSortOrder === 'asc' ? 'A-Z' : 'Z-A'}
            </Button>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectAllReports}
                  onCheckedChange={(checked: boolean | "indeterminate") => {
                    setSelectAllReports(!!checked);
                    const filteredReports = getFilteredReports();
                    setSelectedReportIds(checked ? filteredReports.map(r => r.id) : []);
                  }}
                />
              </TableHead>
              <TableHead>
                <Button variant="ghost" size="sm" onClick={() => setReportSortBy('name')}>
                  Report Name
                  {reportSortBy === 'name' && <ArrowUpDown size={14} className="ml-2" />}
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" size="sm" onClick={() => setReportSortBy('type')}>
                  Type
                  {reportSortBy === 'type' && <ArrowUpDown size={14} className="ml-2" />}
                </Button>
              </TableHead>
              <TableHead>Date Range</TableHead>
              <TableHead>Schedule</TableHead>
              <TableHead>
                <Button variant="ghost" size="sm" onClick={() => setReportSortBy('lastGenerated')}>
                  Last Generated
                  {reportSortBy === 'lastGenerated' && <ArrowUpDown size={14} className="ml-2" />}
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {getFilteredReports().map((report) => (
              <TableRow key={report.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedReportIds.includes(report.id)}
                    onCheckedChange={(checked: boolean | "indeterminate") => {
                      if (checked) {
                        setSelectedReportIds([...selectedReportIds, report.id]);
                      } else {
                        setSelectedReportIds(selectedReportIds.filter(id => id !== report.id));
                        setSelectAllReports(false);
                      }
                    }}
                  />
                </TableCell>
                <TableCell>
    <div>
                    <p className="font-medium">{report.name}</p>
                    <p className="text-sm text-gray-600">{report.description}</p>
    </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {report.type.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </Badge>
                </TableCell>
                <TableCell className="text-sm">
                  {report.dateRange.from.toLocaleDateString()} - {report.dateRange.to.toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {report.schedule?.enabled ? (
                    <Badge className="bg-green-100 text-green-800">
                      {report.schedule.frequency}
                    </Badge>
                  ) : (
                    <Badge variant="outline">Manual</Badge>
                  )}
                </TableCell>
                <TableCell className="text-sm">
                  {report.lastGenerated ? report.lastGenerated.toLocaleString() : 'Never'}
                </TableCell>
                <TableCell>
                  <Badge className={
                    report.status === 'active' ? 'bg-green-100 text-green-800' :
                    report.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }>
                    {report.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal size={14} />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleViewReportDetails(report)}>
                        <Eye size={14} className="mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleGenerateReport(report)}>
                        <FileText size={14} className="mr-2" />
                        Generate Report
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditReport(report)}>
                        <Edit size={14} className="mr-2" />
                        Edit Report
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicateReport(report)}>
                        <Copy size={14} className="mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleScheduleReport(report)}>
                        <Clock size={14} className="mr-2" />
                        Schedule
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {report.status === 'draft' && (
                        <DropdownMenuItem onClick={() => handleActivateReport(report.id)}>
                          <CheckCircle size={14} className="mr-2" />
                          Activate
                        </DropdownMenuItem>
                      )}
                      {report.status === 'active' && (
                        <DropdownMenuItem onClick={() => handleArchiveReport(report.id)}>
                          <Archive size={14} className="mr-2" />
                          Archive
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleExportReport(report, 'pdf')}>
                        <Download size={14} className="mr-2" />
                        Export PDF
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleExportReport(report, 'excel')}>
                        <Download size={14} className="mr-2" />
                        Export Excel
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleExportReport(report, 'csv')}>
                        <Download size={14} className="mr-2" />
                        Export CSV
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => {
                          if (confirm('Are you sure you want to delete this report?')) {
                            handleDeleteReport(report.id);
                          }
                        }}
                      >
                        <Trash2 size={14} className="mr-2" />
                        Delete Report
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell colSpan={8} className="text-center text-sm text-gray-500">
                Showing {getFilteredReports().length} entries
                {selectedReportIds.length > 0 && ` • ${selectedReportIds.length} selected`}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </CardContent>
    </Card>
  );
};

export default FinancialReports;
