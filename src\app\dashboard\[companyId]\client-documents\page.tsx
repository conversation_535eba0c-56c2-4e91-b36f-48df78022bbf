"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableHeader, TableRow, TableCell, TableBody } from "@/components/ui/table";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

type Document = {
  id: string;
  title: string;
  description: string;
  files: File[];
};

export default function DocumentPage() {
  const { companyId } = useParams();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [files, setFiles] = useState<FileList | null>(null);

  const handleUpload = () => {
    if (!title || !description || !files?.length) {
      toast.error("Please fill in all fields and select at least one file.");
      return;
    }

    const newDocument: Document = {
      id: uuidv4(),
      title,
      description,
      files: Array.from(files),
    };

    setDocuments((prev) => [...prev, newDocument]);
    setTitle("");
    setDescription("");
    setFiles(null);
    toast.success("Document(s) uploaded!");
  };

  const handleDelete = (id: string) => {
    setDocuments((prev) => prev.filter((doc) => doc.id !== id));
    toast.success("Document deleted.");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white dark:bg-gray-800 p-6 rounded shadow-md">
        <h1 className="text-2xl font-bold mb-4">Upload Documents</h1>
        <div className="grid gap-4">
          <div>
            <Label>Document Title</Label>
            <Input value={title} onChange={(e) => setTitle(e.target.value)} placeholder="e.g. ID Copy" />
          </div>
          <div>
            <Label>Document Description</Label>
            <Textarea value={description} onChange={(e) => setDescription(e.target.value)} placeholder="e.g. National ID scanned copy." />
          </div>
          <div>
            <Label>Upload Files</Label>
            <Input type="file" multiple onChange={(e) => setFiles(e.target.files)} />
          </div>
          <Button onClick={handleUpload}>Upload</Button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded shadow-md">
        <h2 className="text-xl font-semibold mb-4">Uploaded Documents</h2>
        {documents.length === 0 ? (
          <p className="text-gray-500">No documents uploaded yet.</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Files</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHeader>
            <TableBody>
              {documents.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell>{doc.title}</TableCell>
                  <TableCell>{doc.description}</TableCell>
                  <TableCell>
                    <ul>
                      {doc.files.map((file, index) => (
                        <li key={index} className="text-blue-500 underline">{file.name}</li>
                      ))}
                    </ul>
                  </TableCell>
                  <TableCell>
                    <Button variant="destructive" onClick={() => handleDelete(doc.id)}>
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}
