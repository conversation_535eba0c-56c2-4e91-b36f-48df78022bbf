import { supabaseBrowserClient } from "@/supabase/supbaseClient";
import { createCalendarEvent } from "./calendar_event";

export interface TaskInput {
  title: string;
  dueDate: string; 
  assignedTo: string; 
  linkedToCalendar: boolean;
  status: 'due' | 'progress' | 'done';
  notes?: string;
  companyId: string;
}

export interface TaskRecord extends TaskInput {
  id: number;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
}

const supabase = supabaseBrowserClient;

export async function createTask(input: TaskInput & { createdBy: string }) {
  const { data, error } = await supabase
    .from('tasks')
    .insert([{ 
      title: input.title,
      due_date: input.dueDate,
      assigned_to: input.assignedTo,
      linked_to_calendar: input.linkedToCalendar,
      status: input.status,
      notes: input.notes || null,
      company_id: input.companyId,
      created_by: input.createdBy,
      updated_by: input.createdBy
    }])
    .select('*')
    .single();

  if (error) return { error };
  if (input.linkedToCalendar) {
    const { error: calError } = await createCalendarEvent({
      company_id:       input.companyId,
      belong_to:        input.assignedTo,
      title:           input.title,
      event_date:       input.dueDate,
      type:            "task",
      description:     input.notes ?? null,
      reminder_enabled: true,
      created_by:      input.createdBy,
    });

    if (calError) {
      // you can choose to bubble this error up or just log it:
      console.error("Failed to create calendar_event for task:", calError);
      // if you want to abort the whole operation, uncomment:
      // return { error: calError };
    }
  }
  return { data };
}


export async function fetchTasksByCompany(companyId: string) {
  const { data, error } = await supabase
    .from('tasks')
    .select('*, assignee:assigned_to (full_name)')
    .eq('company_id', companyId)
    .order('due_date', { ascending: true });
  if (error) return { error };
  return { data };
}


export async function fetchMyTasks(companyId: string, userId: string) {
  const { data, error } = await supabase
    .from('tasks')
    .select('*, assignee:assigned_to (full_name)')
    .eq('company_id', companyId)
    .eq('assigned_to', userId)
    .order('due_date', { ascending: true });
  if (error) return { error };
  return { data };
}

/**
 * Update status (and audit updated_by) for a task
 */
export async function updateTaskStatus(
    taskId: number,
    status: TaskRecord['status'],
    updatedBy?: string
  ) {
    const { data, error } = await supabase
      .from('tasks')
      .update({ status, updated_by: updatedBy || null })
      .eq('id', taskId)
      .select()          // ← include this
      .single();
    if (error) return { error };
    return { data };
  }
  
/**
 * Delete a task by id
 */
export async function deleteTask(taskId: number) {
  const { data, error } = await supabase
    .from('tasks')
    .delete()
    .eq('id', taskId)
    .single();
  if (error) return { error };
  return { data };
}