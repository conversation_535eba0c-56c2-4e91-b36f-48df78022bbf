/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { supabaseBrowserClient } from "@/supabase/supbaseClient";
import { useState, useEffect } from "react";

export const getCompany = (userId: string | undefined) => {
    const [profile, setProfile] = useState<any>(null);
    const [companies, setCompanies] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!userId) {
            setError("User ID is required");
            setLoading(false);
            return;
        }

        const fetchProfileAndCompanies = async () => {
            setLoading(true);
            setError(null);

            try {
                
                const { data: userProfile, error: profileError } = await supabaseBrowserClient
                    .from("users")
                    .select("*")
                    .eq("id", userId)
                    .single();

                if (profileError) {
                    throw new Error(profileError.message);
                }
                setProfile(userProfile);

                
                const { data: ceoCompanies, error: ceoError } = await supabaseBrowserClient
                    .from("companies")
                    .select("id, name, ceo_id, employees")
                    .eq("ceo_id", userId);

                if (ceoError) {
                    throw new Error(ceoError.message);
                }

                
                const { data: employeeCompanies, error: empError } = await supabaseBrowserClient
                    .from("companies")
                    .select("id, name, employees")
                    .contains("employees", [userId]);

                if (empError) {
                    throw new Error(empError.message);
                }

                setCompanies([...ceoCompanies, ...employeeCompanies]);

            } catch (err: any) {
                console.error("Error fetching data:", err.message);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchProfileAndCompanies();
    }, [userId]);

    return { profile, companies, loading, error };
};

export const useUserCompanies = (companyIds: string[]) => {
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [companies, setCompanies] = useState<any[]>([]);

    useEffect(() => {
        if (!companyIds || companyIds.length === 0) {
            setLoading(false);
            return;
        }

        const fetchCompanies = async () => {
            try {
                const { data: companyData, error: companyError } = await supabaseBrowserClient
                    .from("companies")
                    .select("*")
                    .in("id", companyIds)
                    .limit(companyIds.length);

                if (companyError) throw companyError;

               
                setCompanies(companyData ?? []); 
            } catch (err: any) {
                console.error("Error fetching companies:", err.message);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchCompanies();
    }, [companyIds]); 

    return { loading, error, companies };
};