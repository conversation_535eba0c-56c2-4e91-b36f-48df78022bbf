/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { fetchMyTasks } from "@/query/tasks";
import { useUserProfile } from "@/query/user";

type RawTaskRow = {
  id: number;
  title: string;
  due_date: string;
  assigned_to: string;
  linked_to_calendar: boolean;
  status: "due" | "progress" | "done";
  notes: string | null;
  assignee: { full_name: string } | null;
};

type Task = {
  id: number;
  title: string;
  dueDate: string;
  assignedToName: string;
  linkedToCalendar: boolean;
  status: "due" | "progress" | "done";
  notes?: string;
};

type User = {
  id: string;
};

export default function TaskTablePage() {
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };

  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const { profile } = useUserProfile(currentUser?.id, companyId);

  useEffect(() => {
    fetch("/api/getUser")
      .then((r) => r.json())
      .then((d) => {
        if (d.success) {
          setCurrentUser(d.user.user);
        }
      })
      .catch(console.error);
  }, []);

  useEffect(() => {
    if (!companyId || !currentUser) return;
    fetchMyTasks(companyId, currentUser.id).then(({ data, error }) => {
      if (error) {
        console.error(error);
        return;
      }
      const formatted = (data as RawTaskRow[]).map((r) => ({
        id: r.id,
        title: r.title,
        dueDate: r.due_date,
        assignedToName: r.assignee?.full_name ?? "(unknown)",
        linkedToCalendar: r.linked_to_calendar,
        status: r.status,
        notes: r.notes ?? undefined,
      }));
      setTasks(formatted);
    });
  }, [companyId, currentUser]);

  const handleViewTask = (task: Task) => {
    alert(`
    Title: ${task.title}
    Due Date: ${task.dueDate}
    Assigned To: ${task.assignedToName}
    Status: ${task.status}
    Linked To Calendar: ${task.linkedToCalendar ? "Yes" : "No"}
    Notes: ${task.notes || "None"}
    `);
  };

  return (
    <div className="container mx-auto p-6">
      {/* Back button at top right */}
      <div className="flex justify-end mb-4">
        <Button variant="outline" onClick={() => router.back()}>
          ← Back
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>My Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white dark:bg-gray-800 rounded-md overflow-hidden">
              <thead>
                <tr className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                  <th className="py-2 px-4 text-left">Title</th>
                  <th className="py-2 px-4 text-left">Due Date</th>
                  <th className="py-2 px-4 text-left">Assigned To</th>
                  <th className="py-2 px-4 text-left">Status</th>
                  <th className="py-2 px-4 text-left">Linked to Calendar</th>
                  <th className="py-2 px-4 text-left">Notes</th>
                  <th className="py-2 px-4 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {tasks.length > 0 ? (
                  tasks.map((task) => (
                    <tr
                      key={task.id}
                      className="border-t border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-300"
                    >
                      <td className="py-2 px-4">{task.title}</td>
                      <td className="py-2 px-4">{task.dueDate}</td>
                      <td className="py-2 px-4">{task.assignedToName}</td>
                      <td className="py-2 px-4 capitalize">{task.status}</td>
                      <td className="py-2 px-4">
                        {task.linkedToCalendar ? "Yes" : "No"}
                      </td>
                      <td className="py-2 px-4">{task.notes || "-"}</td>
                      <td className="py-2 px-4">
                        <Button size="sm" onClick={() => handleViewTask(task)}>
                          View
                        </Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="py-6 text-center text-gray-500">
                      No tasks found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
