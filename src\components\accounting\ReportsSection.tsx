import React, { useState } from 'react';
import { FinancialReport, ReportTemplate, Account } from '@/types/accounting';
import { useToast } from '@/hooks/use-toast';

interface ReportsSectionProps {
  // Only pass essential data and minimal callbacks
  accounts: Account[]; // For report generation
}

const ReportsSection: React.FC<ReportsSectionProps> = ({ accounts }) => {
  const { toast } = useToast();

  // Move all report-related state here
  const [reports, setReports] = useState<FinancialReport[]>([]);
  const [editingReport, setEditingReport] = useState<FinancialReport | null>(null);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [selectedReportIds, setSelectedReportIds] = useState<string[]>([]);
  const [selectAllReports, setSelectAllReports] = useState(false);
  const [reportSearchTerm, setReportSearchTerm] = useState('');
  const [reportFilterType, setReportFilterType] = useState<string>('all');
  const [reportFilterStatus, setReportFilterStatus] = useState<'all' | 'draft' | 'active' | 'archived'>('all');
  const [reportSortBy, setReportSortBy] = useState<'name' | 'type' | 'created' | 'lastGenerated'>('name');
  const [reportSortOrder, setReportSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedReport, setSelectedReport] = useState<FinancialReport | null>(null);
  const [showReportDetailsDialog, setShowReportDetailsDialog] = useState(false);
  const [showReportExportDialog, setShowReportExportDialog] = useState(false);
  const [showReportDuplicateDialog, setShowReportDuplicateDialog] = useState(false);
  const [showReportHistoryDialog, setShowReportHistoryDialog] = useState(false);
  const [showReportScheduleDialog, setShowReportScheduleDialog] = useState(false);
  const [showReportPreviewDialog, setShowReportPreviewDialog] = useState(false);
  const [generatedReportData, setGeneratedReportData] = useState<any>(null);

  const [reportForm, setReportForm] = useState<{
    name: string;
    type: FinancialReport['type'];
    description: string;
    dateRange: {
      from: Date;
      to: Date;
    };
    parameters: {
      accounts: string[];
      includeInactive: boolean;
      groupBy: 'account' | 'category' | 'month' | 'quarter';
      comparison: 'none' | 'previous_period' | 'previous_year';
      format: 'summary' | 'detailed';
    };
    schedule: {
      enabled: boolean;
      frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
      recipients: string[];
    };
  }>({
    name: '',
    type: 'balance_sheet',
    description: '',
    dateRange: {
      from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      to: new Date()
    },
    parameters: {
      accounts: [],
      includeInactive: false,
      groupBy: 'account',
      comparison: 'none',
      format: 'summary'
    },
    schedule: {
      enabled: false,
      frequency: 'monthly',
      recipients: []
    }
  });

  // Calculate financial metrics for report generation
  const calculateMetrics = () => {
    const assets = accounts.filter(a => a.type === 'asset').reduce((sum, a) => sum + a.balance, 0);
    const liabilities = accounts.filter(a => a.type === 'liability').reduce((sum, a) => sum + a.balance, 0);
    const equity = accounts.filter(a => a.type === 'equity').reduce((sum, a) => sum + a.balance, 0);
    const revenue = accounts.filter(a => a.type === 'revenue').reduce((sum, a) => sum + a.balance, 0);
    const expenses = accounts.filter(a => a.type === 'expense').reduce((sum, a) => sum + a.balance, 0);

    return {
      totalAssets: assets,
      totalLiabilities: liabilities,
      totalEquity: equity,
      totalRevenue: revenue,
      totalExpenses: expenses,
      netIncome: revenue - expenses,
      workingCapital: assets - liabilities
    };
  };

  const metrics = calculateMetrics();

  // Financial Reports CRUD Handlers
  const handleSaveReport = () => {
    if (editingReport) {
      // Update existing report
      const updatedReports = reports.map(report =>
        report.id === editingReport.id
          ? {
              ...report,
              ...reportForm,
              lastGenerated: undefined // Reset generation status on edit
            }
          : report
      );
      setReports(updatedReports);
      toast({
        title: "Success",
        description: "Report updated successfully!",
      });
    } else {
      // Create new report
      const newReport: FinancialReport = {
        id: Date.now().toString(),
        name: reportForm.name,
        type: reportForm.type,
        description: reportForm.description,
        dateRange: reportForm.dateRange,
        parameters: reportForm.parameters,
        schedule: reportForm.schedule.enabled ? reportForm.schedule : undefined,
        createdBy: 'Current User',
        createdAt: new Date(),
        status: 'draft'
      };
      setReports([...reports, newReport]);
      toast({
        title: "Success",
        description: "Report created successfully!",
      });
    }

    // Reset form and close dialog
    setReportForm({
      name: '',
      type: 'balance_sheet',
      description: '',
      dateRange: {
        from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        to: new Date()
      },
      parameters: {
        accounts: [],
        includeInactive: false,
        groupBy: 'account',
        comparison: 'none',
        format: 'summary'
      },
      schedule: {
        enabled: false,
        frequency: 'monthly',
        recipients: []
      }
    });
    setEditingReport(null);
    setShowReportDialog(false);
  };

  const handleDeleteReport = (reportId: string) => {
    const updatedReports = reports.filter(report => report.id !== reportId);
    setReports(updatedReports);
    toast({
      title: "Success",
      description: "Report deleted successfully!",
    });
  };

  const handleGenerateReport = (report: FinancialReport) => {
    // Simulate report generation
    const reportData = generateReportData(report);
    setGeneratedReportData(reportData);

    // Update report with generation timestamp
    const updatedReports = reports.map(r =>
      r.id === report.id
        ? { ...r, lastGenerated: new Date(), status: 'active' as const }
        : r
    );
    setReports(updatedReports);

    setShowReportPreviewDialog(true);
    toast({
      title: "Success",
      description: `${report.name} generated successfully!`,
    });
  };

  const generateReportData = (report: FinancialReport) => {
    // Generate mock report data based on report type
    switch (report.type) {
      case 'balance_sheet':
        return {
          title: 'Balance Sheet',
          period: `${report.dateRange.from.toLocaleDateString()} - ${report.dateRange.to.toLocaleDateString()}`,
          sections: {
            assets: accounts.filter(a => a.type === 'asset'),
            liabilities: accounts.filter(a => a.type === 'liability'),
            equity: accounts.filter(a => a.type === 'equity')
          },
          totals: {
            totalAssets: metrics.totalAssets,
            totalLiabilities: metrics.totalLiabilities,
            totalEquity: metrics.totalEquity
          }
        };
      case 'profit_loss':
        return {
          title: 'Profit & Loss Statement',
          period: `${report.dateRange.from.toLocaleDateString()} - ${report.dateRange.to.toLocaleDateString()}`,
          sections: {
            revenue: accounts.filter(a => a.type === 'revenue'),
            expenses: accounts.filter(a => a.type === 'expense')
          },
          totals: {
            totalRevenue: metrics.totalRevenue,
            totalExpenses: metrics.totalExpenses,
            netIncome: metrics.netIncome
          }
        };
      case 'trial_balance':
        return {
          title: 'Trial Balance',
          period: `As of ${report.dateRange.to.toLocaleDateString()}`,
          accounts: accounts.map(account => ({
            ...account,
            debitBalance: account.type === 'asset' || account.type === 'expense' ? account.balance : 0,
            creditBalance: account.type === 'liability' || account.type === 'equity' || account.type === 'revenue' ? account.balance : 0
          })),
          totals: {
            totalDebits: accounts.filter(a => a.type === 'asset' || a.type === 'expense').reduce((sum, a) => sum + a.balance, 0),
            totalCredits: accounts.filter(a => a.type === 'liability' || a.type === 'equity' || a.type === 'revenue').reduce((sum, a) => sum + a.balance, 0)
          }
        };
      default:
        return {
          title: report.name,
          period: `${report.dateRange.from.toLocaleDateString()} - ${report.dateRange.to.toLocaleDateString()}`,
          data: 'Report data would be generated here'
        };
    }
  };

  const handleEditReport = (report: FinancialReport) => {
    setEditingReport(report);
    setReportForm({
      name: report.name,
      type: report.type,
      description: report.description,
      dateRange: report.dateRange,
      parameters: report.parameters,
      schedule: report.schedule || {
        enabled: false,
        frequency: 'monthly',
        recipients: []
      }
    });
    setShowReportDialog(true);
  };

  const handleViewReportDetails = (report: FinancialReport) => {
    setSelectedReport(report);
    setShowReportDetailsDialog(true);
  };

  const handleDuplicateReport = (report: FinancialReport) => {
    const duplicatedReport: FinancialReport = {
      ...report,
      id: Date.now().toString(),
      name: `${report.name} (Copy)`,
      createdBy: 'Current User',
      createdAt: new Date(),
      lastGenerated: undefined,
      status: 'draft'
    };
    setReports([...reports, duplicatedReport]);
    toast({
      title: "Success",
      description: "Report duplicated successfully!",
    });
  };

  const handleArchiveReport = (reportId: string) => {
    const updatedReports = reports.map(report =>
      report.id === reportId
        ? { ...report, status: 'archived' as const }
        : report
    );
    setReports(updatedReports);
    toast({
      title: "Success",
      description: "Report archived successfully!",
    });
  };

  const handleActivateReport = (reportId: string) => {
    const updatedReports = reports.map(report =>
      report.id === reportId
        ? { ...report, status: 'active' as const }
        : report
    );
    setReports(updatedReports);
    toast({
      title: "Success",
      description: "Report activated successfully!",
    });
  };

  const handleExportReport = (report: FinancialReport, format: 'pdf' | 'excel' | 'csv') => {
    toast({
      title: "Export Started",
      description: `${report.name} is being exported as ${format.toUpperCase()}...`,
    });

    // Simulate export process
    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: `${report.name}.${format} has been downloaded.`,
      });
    }, 2000);
  };

  const getFilteredReports = () => {
    const filtered = reports.filter(report => {
      const matchesSearch = report.name.toLowerCase().includes(reportSearchTerm.toLowerCase()) ||
                           report.description.toLowerCase().includes(reportSearchTerm.toLowerCase());
      const matchesType = reportFilterType === 'all' || report.type === reportFilterType;
      const matchesStatus = reportFilterStatus === 'all' || report.status === reportFilterStatus;

      return matchesSearch && matchesType && matchesStatus;
    });

    // Sort reports
    filtered.sort((a, b) => {
      let aValue, bValue;
      switch (reportSortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'created':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'lastGenerated':
          aValue = a.lastGenerated ? new Date(a.lastGenerated).getTime() : 0;
          bValue = b.lastGenerated ? new Date(b.lastGenerated).getTime() : 0;
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return reportSortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return reportSortOrder === 'asc' ?
        (aValue as number) - (bValue as number) :
        (bValue as number) - (aValue as number);
    });

    return filtered;
  };

  const filteredReports = getFilteredReports();

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Financial Reports</h2>
      <table className="min-w-full bg-white border">
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Date Range</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {filteredReports.map(report => (
            <tr key={report.id}>
              <td>{report.name}</td>
              <td>{report.type}</td>
              <td>{`${new Date(report.dateRange.from).toLocaleDateString()} - ${new Date(report.dateRange.to).toLocaleDateString()}`}</td>
              <td>{report.status}</td>
              <td>
                <button onClick={() => handleEditReport(report)}>Edit</button>
                <button onClick={() => handleDeleteReport(report.id)}>Delete</button>
                <button onClick={() => handleGenerateReport(report)}>Generate</button>
                <button onClick={() => handleExportReport(report, 'pdf')}>Export PDF</button>
                <button onClick={() => handleViewReportDetails(report)}>Details</button>
                <button onClick={() => handleDuplicateReport(report)}>Duplicate</button>
                <button onClick={() => handleArchiveReport(report.id)}>Archive</button>
                <button onClick={() => handleActivateReport(report.id)}>Activate</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* Add dialogs, bulk actions, filters, etc. as needed */}
    </div>
  );
};

export default ReportsSection;