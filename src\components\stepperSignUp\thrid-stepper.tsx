
import { useSignupValues } from '@/hooks/signup-values';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input } from '@/components/ui/input';
import Typography from '@/components/ui/typography';

const formSchema = z.object({
    numberOfUsers: z
        .number()
        .min(1, "Number of users must be at least 1")
        .max(100000000, "Number of users cannot exceed 100,000,000"),
});

const ThirdStepper = () => {
    const { setCurrentStep, updateFormData, formData  } = useSignupValues();

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            numberOfUsers: formData.numberOfUsers || 1,
        },
    });

    const handleNext = (data: z.infer<typeof formSchema>) => {
        updateFormData(data);
        setCurrentStep(4);
    };

    const handleBack = () => {
        setCurrentStep(2);
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleNext)}>
                <Typography
                    text="Step 3: Number of Users"
                    variant="h2"
                    className="text-2xl text-center font-bold text-blue-600 dark:text-blue-400"
                />
                <Typography
                    text="Choose the number of users for your account. If you select 1 user, it means only one person will use the system. If you choose 2 users, it means 1 as admin/business owner and the other as an employee."
                    variant="p"
                    className="mb-4 text-gray-600 dark:text-gray-400 text-center"
                />
                <Typography
                    text="Note: At the end of your 14-day trial, you will be charged based on the number of users you select."
                    variant="p"
                    className="mb-6 text-red-600 dark:text-red-400 text-center"
                />
                <FormField
                    name="numberOfUsers"
                    render={({ field }) => (
                        <FormItem>
                            <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Number of Users</label>
                            <FormControl>
                                <Input
                                    {...field}
                                    type="number"
                                    placeholder="Enter number of users (1 - 100,000,000)"
                                    className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg"
                                    onChange={(e) =>
                                        field.onChange(Number(e.target.value)) 
                                    }
                                    value={field.value ?? ''} 
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <div className="flex justify-between mt-6">
                    <Button type="button" onClick={handleBack} className="bg-gray-400 hover:bg-gray-300 text-white">
                        Back
                    </Button>
                    <Button type="submit" className="bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 hover:bg-blue-500 dark:text-white text-white">
                        Next
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default ThirdStepper;
