// Utility function to format currency
export const formatCurrency = (amount: number, currency: string = 'ZAR') => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

// Utility function to calculate financial metrics from accounts
import { Account } from '@/types/accounting';

export const calculateMetrics = (accounts: Account[]) => {
  const assets = accounts.filter(a => a.type === 'asset').reduce((sum, a) => sum + a.balance, 0);
  const liabilities = accounts.filter(a => a.type === 'liability').reduce((sum, a) => sum + a.balance, 0);
  const equity = accounts.filter(a => a.type === 'equity').reduce((sum, a) => sum + a.balance, 0);
  const revenue = accounts.filter(a => a.type === 'revenue').reduce((sum, a) => sum + a.balance, 0);
  const expenses = accounts.filter(a => a.type === 'expense').reduce((sum, a) => sum + a.balance, 0);

  return {
    totalAssets: assets,
    totalLiabilities: liabilities,
    totalEquity: equity,
    totalRevenue: revenue,
    totalExpenses: expenses,
    netIncome: revenue - expenses,
    workingCapital: assets - liabilities
  };
}; 