/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent
} from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { getInvoiceById, updateInvoice } from "@/query/invoices";

interface InvoiceItem {
  id: number;
  item: string;
  quantity: number;
  rate: number;
  tax?: number;
  discount?: number;
}

interface PaymentSchedule {
  dueDate: string;
  amount: number;
  note?: string;
}

interface InvoiceData {
  id: string;
  company_id: string;
  reference_number: string;
  invoice_date: string;
  due_date?: string;
  shipping_address: string;
  notes?: string;
  signature?: string;
  discount: number;
  tax: number;
  status: string;
  quotation_items: InvoiceItem[];
  payment_schedule: PaymentSchedule[];
  totals: {
    sub_total: number;
    total_tax: number;
    total_after_discount: number;
  };
  client?: {
    full_name: string;
    email: string;
    company_name: string;
  };
  is_converted_to_invoice: boolean;
}

export default function EditInvoicePage() {
  const router = useRouter();
  const params = useParams() as any;
  const invoiceId = params.invoiceId || params.id;

  const [invoice, setInvoice] = useState<InvoiceData | null>(null);
  const [loading, setLoading] = useState(true);

  // Form state
  const [companyId, setCompanyId] = useState("");
  const [referenceNumber, setReferenceNumber] = useState("");
  const [invoiceDate, setInvoiceDate] = useState("");
  const [dueDate, setDueDate] = useState("");
  const [shippingAddress, setShippingAddress] = useState("");
  const [notes, setNotes] = useState("");
  const [signature, setSignature] = useState("");
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [status, setStatus] = useState("pending");
  const [isConverted, setIsConverted] = useState(false);

  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [paymentSchedule, setPaymentSchedule] = useState<PaymentSchedule[]>([]);

  useEffect(() => {
    async function load() {
      if (!invoiceId) return setLoading(false);
      const { data, error } = await getInvoiceById(invoiceId);
      if (error) {
        toast.error("Error fetching invoice");
      } else {
        setInvoice(data);
        setCompanyId(data.company_id);
        setReferenceNumber(data.reference_number);
        setInvoiceDate(data.invoice_date);
        setDueDate(data.due_date || "");
        setShippingAddress(data.shipping_address);
        setNotes(data.notes || "");
        setSignature(data.signature || "");
        setDiscount(data.discount);
        setTax(data.tax);
        setStatus(data.status);
        setIsConverted(data.is_converted_to_invoice);
        setItems(data.quotation_items);
        setPaymentSchedule(data.payment_schedule || []);
      }
      setLoading(false);
    }
    load();
  }, [invoiceId]);

  // Totals
  const subTotal = items.reduce((sum, i) => sum + i.rate * i.quantity, 0);
  const totalTax = (subTotal * tax) / 100;
  const afterDiscount = subTotal + totalTax - discount;
  const grandTotal = invoice?.totals.total_after_discount ?? afterDiscount;

  // Handlers
  const handleItemChange = (idx: number, field: keyof InvoiceItem, val: string | number) => {
    const copy = [...items];
    copy[idx] = { ...copy[idx], [field]: typeof val === "string" ? parseFloat(val) || 0 : val };
    setItems(copy);
  };
  const addItem = () =>
    setItems(prev => [...prev, { id: Date.now(), item: "", quantity: 1, rate: 0 }]);
  const removeItem = (id: number) =>
    setItems(prev => prev.filter(i => i.id !== id));

  const handleScheduleChange = (idx: number, field: keyof PaymentSchedule, val: string | number) => {
    const copy = [...paymentSchedule];
    copy[idx] = {
      ...copy[idx],
      [field]: field === "dueDate" ? (val as string) : Number(val),
    };
    setPaymentSchedule(copy);
  };
  const addSchedule = () =>
    setPaymentSchedule(prev => [...prev, { dueDate: "", amount: 0, note: "" }]);
  const removeSchedule = (i: number) =>
    setPaymentSchedule(prev => prev.filter((_, idx) => idx !== i));

  const handleUpdate = async () => {
    if (!invoice) return;
    const payload = {
      company_id: companyId,
      reference_number: referenceNumber,
      invoice_date: invoiceDate,
      due_date: dueDate,
      shipping_address: shippingAddress,
      notes,
      signature,
      discount,
      tax,
      status,
      is_converted_to_invoice: isConverted,
      quotation_items: items,
      payment_schedule: paymentSchedule,
    };
    const { error } = await updateInvoice(invoice.id, payload);
    if (error) toast.error("Failed to update invoice");
    else {
      toast.success("Invoice updated successfully!");
      router.back();
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen"><p>Loading...</p></div>;
  }
  if (!invoice) {
    return <div className="flex items-center justify-center min-h-screen"><p>No invoice found.</p></div>;
  }

  const client = invoice.client || { full_name: "", email: "", company_name: "" };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Edit Invoice</h1>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-1" /> Back
        </Button>
      </div>

      {/* General */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader><CardTitle>General</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Company ID</label>
              <Input value={companyId} onChange={e => setCompanyId(e.target.value)} />
            </div>
            <div>
              <label className="block text-sm font-medium">Status</label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Select status" /></SelectTrigger>
                <SelectContent>
                  {["pending","approved","rejected"].map(s => <SelectItem key={s} value={s}>{s}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label>Signature</label>
              <Input placeholder="e.g. John Doe" value={signature} onChange={e => setSignature(e.target.value)} />
            </div>
            <div className="flex items-center space-x-2">
              <label>Converted to Invoice</label>
              <input type="checkbox" checked={isConverted} onChange={e => setIsConverted(e.target.checked)} />
              
            </div>
          </CardContent>
        </Card>

        {/* Reference & Dates */}
        <Card>
          <CardHeader><CardTitle>Reference & Dates</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Invoice Number</label>
              <Input value={referenceNumber} onChange={e => setReferenceNumber(e.target.value)} />
            </div>
            <div>
              <label>Invoice Date</label>
              <Input type="date" value={invoiceDate} onChange={e => setInvoiceDate(e.target.value)} />
            </div>
            <div>
              <label>Due Date</label>
              <Input type="date" value={dueDate} onChange={e => setDueDate(e.target.value)} />
            </div>
          </CardContent>
        </Card>

        {/* Shipping & Notes */}
        <Card>
          <CardHeader><CardTitle>Shipping & Notes</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Shipping Address</label>
              <Input value={shippingAddress} onChange={e => setShippingAddress(e.target.value)} />
            </div>
            <div>
              <label>Notes</label>
              <Input value={notes} onChange={e => setNotes(e.target.value)} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Items */}
      <Card>
        <CardHeader><CardTitle>Items</CardTitle></CardHeader>
        <CardContent>
          <div className="overflow-auto mb-3">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Rate</TableHead>
                  <TableHead>Tax %</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((it, i) => {
                  const amt = it.quantity * it.rate;
                  return (
                    <TableRow key={it.id}>
                      <TableCell>
                        <Input
                          value={it.item}
                          onChange={e => handleItemChange(i, "item", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.quantity}
                          onChange={e => handleItemChange(i, "quantity", e.target.value)}
                          className="w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.rate}
                          onChange={e => handleItemChange(i, "rate", e.target.value)}
                          className="w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.tax || 0}
                          onChange={e => handleItemChange(i, "tax", e.target.value)}
                          className="w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.discount || 0}
                          onChange={e => handleItemChange(i, "discount", e.target.value)}
                          className="w-20"
                        />
                      </TableCell>
                      <TableCell>R{amt.toFixed(2)}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => removeItem(it.id)}>
                          Remove
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
          <Button variant="outline" onClick={addItem}>+ Add Row</Button>
        </CardContent>
      </Card>

      {/* Payment Schedule */}
      <Card>
        <CardHeader><CardTitle>Payment Schedule</CardTitle></CardHeader>
        <CardContent>
          <div className="overflow-auto mb-3">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Note</TableHead>
                  <TableHead>Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paymentSchedule.map((ps, i) => (
                  <TableRow key={i}>
                    <TableCell>
                      <Input
                        type="date"
                        value={ps.dueDate}
                        onChange={e => handleScheduleChange(i, "dueDate", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={ps.amount}
                        onChange={e => handleScheduleChange(i, "amount", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        value={ps.note}
                        onChange={e => handleScheduleChange(i, "note", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" onClick={() => removeSchedule(i)}>
                        Remove
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <Button variant="outline" onClick={addSchedule}>+ Add Row</Button>
        </CardContent>
      </Card>

      {/* Totals & Tax/Discount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader><CardTitle>Tax & Discount</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Tax %</label>
              <Input
                type="number"
                value={tax}
                onChange={e => setTax(parseFloat(e.target.value) || 0)}
              />
            </div>
            <div>
              <label>Discount</label>
              <Input
                type="number"
                value={discount}
                onChange={e => setDiscount(parseFloat(e.target.value) || 0)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader><CardTitle>Totals</CardTitle></CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Sub-total:</span><span>R{subTotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Tax:</span><span>R{totalTax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>After Discount:</span><span>R{afterDiscount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Grand Total:</span><span>R{grandTotal.toFixed(2)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bill To */}
      <Card>
        <CardHeader><CardTitle>Bill To</CardTitle></CardHeader>
        <CardContent>
          <p className="font-semibold">{client.full_name}</p>
          <p>{shippingAddress}</p>
          <p>Email: {client.email}</p>
        </CardContent>
      </Card>

      {/* Update Button */}
      <div className="flex justify-end">
        <Button onClick={handleUpdate}>Update Invoice</Button>
      </div>
    </div>
  );
}
