/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";

interface SignupValuesState {
    currentStep: number;
    totalSteps: number;
    formData: Record<string, any>;
    setCurrentStep: (step: number) => void;
    updateFormData: (data: Record<string, any>) => void;
}

export const useSignupValues = create<SignupValuesState>((set) => ({
    currentStep: 1,
    totalSteps: 6,
    formData: {},
    setCurrentStep: (step) => set({ currentStep: step }),
    updateFormData: (data) =>
        set((state) => ({ formData: { ...state.formData, ...data } })),
}));
