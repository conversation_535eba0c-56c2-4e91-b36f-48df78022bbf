
"use client"
import { AppSidebar } from "@/components/dashboard/sidebar/app-sidebar"
import { <PERSON>barProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { useUserProfile } from "@/query/user";
import { User } from "@/types/app";
import { useTheme } from "next-themes";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { HiSun, HiMoon, HiDesktopComputer } from "react-icons/hi";
import { redirect } from "next/navigation"
import useAutoLogout from "@/actionsApi/useAutoLogout";
export default function Layout({ children }: { children: React.ReactNode }) {
  useAutoLogout();
    const { setTheme } = useTheme();
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [start, setStarting] = useState(true);
    const params = useParams(); 
      const companyId = params?.companyId as string; 
    
      const { loading } = useUserProfile(currentUser?.id, companyId);
    useEffect(() => {
        const fetchUser = async () => {
          try {
            const response = await fetch("/api/getUser");
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            const data = await response.json();
            
    
            if (data.success) {
              setCurrentUser(data.user.user);
            } else {
              console.error("User fetch error:", data.error);
            }
          } catch (error) {
            console.error("Error fetching user:", error);
          } finally {
            setStarting(false);
          }
        };
    
        fetchUser();
      }, []);

      if (loading || start) {
        return (
          <div className="flex items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 border-solid"></div>
          </div>
        );
      }
    if (!currentUser) {
       redirect('/auth/login')
    }
    
  return (
    <SidebarProvider className="">
      <AppSidebar />
      <main className="w-full">
        <SidebarTrigger />
        <div className="absolute top-1 right-5">
            <div className="flex space-x-2">
                <button onClick={() => setTheme('light')} aria-label="Light Mode">
                    <HiSun size={24} className="text-yellow-500" />
                </button>
                <button onClick={() => setTheme('dark')} aria-label="Dark Mode">
                    <HiMoon size={24} className="text-gray-500" />
                </button>
                <button onClick={() => setTheme('system')} aria-label="System Mode">
                    <HiDesktopComputer size={24} className="text-blue-500" />
                </button>
            </div>
        </div>
        <div>
          {children}
        </div>

      </main>
    </SidebarProvider>
  )
}
