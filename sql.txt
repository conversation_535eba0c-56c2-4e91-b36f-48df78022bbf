CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT DEFAULT 'Employee' CHECK (role IN ('CEO', 'Employee', 'Client')),
    company_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Allow users to view and update their own data
CREATE POLICY select_own_user ON users
    FOR SELECT
    USING (id = current_setting('app.user_id')::UUID);

CREATE POLICY update_own_user ON users
    FOR UPDATE
    USING (id = current_setting('app.user_id')::UUID);


CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    industry VARCHAR(255),
    country VARCHAR(100),
    city VARCHAR(100),
    address TEXT,
    currency VARCHAR(50),
    years_in_business VARCHAR(50),
    ceo_id TEXT,
    employees TEXT[], -- Array of employee IDs
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS on companies table
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Allow CEOs to manage their own company
CREATE POLICY manage_own_company ON companies
    FOR ALL
    USING (ceo_id = current_setting('app.user_id'));


CREATE TABLE employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role TEXT DEFAULT 'Employee',
    company_id TEXT NOT NULL,
    created_by TEXT,
    permissions TEXT[], -- Array of permissions
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS on employees table
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Allow CEOs to manage employees in their company
CREATE POLICY manage_employees ON employees
    FOR ALL
    USING (company_id = (SELECT company_id FROM users WHERE id = current_setting('app.user_id')::UUID));


CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (id, email, full_name, role)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', 'Employee');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to Add User to Users Table
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION handle_new_user();


CREATE OR REPLACE FUNCTION add_employee_to_company(employee_id UUID, company_id TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE companies SET employees = array_append(employees, employee_id::TEXT)
    WHERE id = company_id;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION set_user_context(user_id TEXT)
RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.user_id', user_id, TRUE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;


CREATE OR REPLACE FUNCTION update_employee_permissions(employee_id UUID, new_permissions TEXT[])
RETURNS VOID AS $$
BEGIN
    UPDATE employees SET permissions = new_permissions WHERE id = employee_id;
END;
$$ LANGUAGE plpgsql;


