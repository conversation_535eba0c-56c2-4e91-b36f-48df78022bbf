"use client"; // Required for client-side interactivity

import { <PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function ViewTransactionsPage() {
  const transactions = [
    {
      id: "1",
      date: "2023-10-01",
      account: "Cash",
      debit: 500,
      credit: 0,
      payer: "John Doe",
      method: "Cash",
    },
    {
      id: "2",
      date: "2023-10-05",
      account: "Accounts Receivable",
      debit: 0,
      credit: 1000,
      payer: "<PERSON>",
      method: "Bank Transfer",
    },
  ];

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>View Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Account</TableHead>
                <TableHead>Debit</TableHead>
                <TableHead>Credit</TableHead>
                <TableHead>Payer</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>{transaction.account}</TableCell>
                  <TableCell>${transaction.debit}</TableCell>
                  <TableCell>${transaction.credit}</TableCell>
                  <TableCell>{transaction.payer}</TableCell>
                  <TableCell>{transaction.method}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline">
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}