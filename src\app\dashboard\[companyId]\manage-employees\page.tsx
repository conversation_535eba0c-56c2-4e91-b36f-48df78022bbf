/* src/app/dashboard/[companyId]/manage-employees/page.tsx */
"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Plus } from "lucide-react";
import { getEmployeesByCompany, EmployeeRecord } from "@/query/employees";

export default function ManageEmployeesPage() {
  const { companyId } = useParams() as { companyId: string };
  const [employees, setEmployees] = useState<EmployeeRecord[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (!companyId) return;
    getEmployeesByCompany(companyId).then(({ data, error }) => {
      if (error) {
        console.error(error);
      } else {
        setEmployees(data || []);
      }
    });
  }, [companyId]);

  const filtered = employees.filter(emp =>
    emp.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    emp.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto p-4">
      {/* Top header */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0"></h1>
        <Link href={`/dashboard/${companyId}/add-new-employee`}>
          <Button className="flex items-center">
            <Plus className="h-4 w-4 mr-2" /> New Employee
          </Button>
        </Link>
      </div>

      <Card>
        {/* Search bar inside card header */}
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-center">
            <CardTitle className="mb-4 md:mb-0">Manage Employees</CardTitle>
            <div className="flex gap-2 w-full md:w-1/2">
              <Input
                placeholder="Search name or email..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full sm:w-64"
              />
              <Button className="flex items-center">
                <Search className="h-4 w-4 mr-2" /> Search
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Table of employees */}
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader className="bg-gray-100 dark:bg-gray-800">
                <TableRow>
                  
                  <TableHead>Name</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filtered.length > 0 ? (
                  filtered.map((emp) => (
                    <TableRow key={emp.id}>
                      
                      <TableCell>{emp.full_name}</TableCell>
                      <TableCell>{emp.address ?? "No Address"}</TableCell>
                      <TableCell>{emp.email}</TableCell>
                      <TableCell>{emp.phone ?? "No Phone"}</TableCell>
                      <TableCell>
                        <Link href={`/dashboard/${companyId}/employees/${emp.id}/view`}>
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">
                      No employees found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
