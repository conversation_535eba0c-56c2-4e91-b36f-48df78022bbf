import { supabaseBrowserClient } from "@/supabase/supbaseClient";

export type EmployeeRecord = {
  id: string;
  full_name: string;
  address: string;
  email: string;
  phone: string;
  status: "Active" | "Inactive";
  company_id: string;
};

export async function getEmployeesByCompany(companyId: string) {
    const supabase = supabaseBrowserClient;
  
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      
      .contains('company_id', [companyId])
     
  
    if (error) {
      return { error };
    }
  
    return { data };
  }
  