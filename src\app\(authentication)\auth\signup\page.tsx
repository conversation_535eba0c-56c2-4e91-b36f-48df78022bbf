'use client'
import React, { useState } from 'react';
import { BsSlack } from 'react-icons/bs';
import { <PERSON><PERSON><PERSON>, HiMoon, HiDesktopComputer } from 'react-icons/hi';
import Typography from '@/components/ui/typography';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTheme } from 'next-themes';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { signUpUser } from '@/actionsApi/register-user';
import { toast } from 'sonner';

const Signup = () => {
  const { setTheme } = useTheme();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const router = useRouter();

  // 1) Extended schema
  const formSchema = z
    .object({
      fullName: z.string().min(2, { message: 'Full name must be at least 2 characters long' }),
      email: z.string().email({ message: 'Please provide a valid email address' }),
      password: z.string().min(8, { message: 'Password must be at least 8 characters long' }),
      confirmPassword: z.string().min(8, { message: 'Please confirm your password' }),
      phone: z.string().min(7, { message: 'Please provide a valid phone number' }),
      gender: z.enum(['Male', 'Female', 'Other'], {
        errorMap: () => ({ message: 'Select Male, Female or Other' }),
      }),
      billingStreet: z.string().min(1, { message: 'Street address is required' }),
      billingCity: z.string().min(1, { message: 'City is required' }),
      billingState: z.string().min(1, { message: 'State/Province is required' }),
      billingZip: z.string().min(1, { message: 'Postal code is required' }),
      billingCountry: z.string().min(1, { message: 'Country is required' }),
    })
    .refine((d) => d.password === d.confirmPassword, {
      path: ['confirmPassword'],
      message: 'Passwords do not match',
    });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
      gender: 'Other',
      billingStreet: '',
      billingCity: '',
      billingState: '',
      billingZip: '',
      billingCountry: '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsAuthenticating(true);
    try {
      const response = await signUpUser(values);
      if (response.error) {
        toast.error(response.error);
        setIsAuthenticating(false);
      } else if (response.message === 'User signed up successfully.') {
        toast.success('Signup successful! Redirecting...');
        form.reset();
        router.push('/auth/verification');
      } else {
        toast.error('An unexpected error occurred.');
        setIsAuthenticating(false);
      }
    } catch (err) {
      console.error('Error during registration:', err);
      toast.error('An unexpected error occurred. Please try again later.');
      setIsAuthenticating(false);
    }
  };

  return (
    <div className="min-h-screen w-full p-4 sm:p-8 grid place-content-center bg-gray-200 dark:bg-gray-900">
      <div className="absolute top-5 right-5">
        <div className="flex space-x-2">
          <button onClick={() => setTheme('light')} aria-label="Light Mode">
            <HiSun size={24} className="text-yellow-500" />
          </button>
          <button onClick={() => setTheme('dark')} aria-label="Dark Mode">
            <HiMoon size={24} className="text-gray-500" />
          </button>
          <button onClick={() => setTheme('system')} aria-label="System Mode">
            <HiDesktopComputer size={24} className="text-blue-500" />
          </button>
        </div>
      </div>

      <div className="w-full max-w-2xl mx-auto border border-neutral-200 p-5 sm:p-8 rounded-lg shadow-lg bg-gray-100 dark:bg-gray-800">
        <div className="flex justify-center items-center gap-3 mb-4">
          <BsSlack size={30} className="text-blue-500" />
          <div className="flex">
            <Typography text="Seben" variant="h2" />
            <Typography text="za" variant="h2" className="text-blue-500" />
          </div>
        </div>

        <Typography
          text="Create your account"
          variant="h3"
          className="mb-3 text-gray-800 dark:text-white text-center"
        />
        <Typography
          text="Join us and start your journey to experience the best of our services."
          variant="p"
          className="opacity-90 mb-7 text-gray-600 dark:text-gray-400 text-center"
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Full Name */}
              <FormField name="fullName" render={({ field }) => (
                <FormItem>
                  <Typography text="Full Name" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="John Doe" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Email */}
              <FormField name="email" render={({ field }) => (
                <FormItem>
                  <Typography text="Email" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Password */}
              <FormField name="password" render={({ field }) => (
                <FormItem>
                  <Typography text="Password" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input type="password" placeholder="***********" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Confirm Password */}
              <FormField name="confirmPassword" render={({ field }) => (
                <FormItem>
                  <Typography text="Confirm Password" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input type="password" placeholder="***********" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Phone */}
              <FormField name="phone" render={({ field }) => (
                <FormItem>
                  <Typography text="Phone" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input type="tel" placeholder="+1234567890" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Gender */}
              <FormField name="gender" render={({ field }) => (
                <FormItem>
                  <Typography text="Gender" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <select {...field} className="w-full px-3 py-2 border rounded dark:bg-gray-700 dark:text-white">
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Billing Street */}
              <FormField name="billingStreet" render={({ field }) => (
                <FormItem>
                  <Typography text="Street Address" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="123 Main St" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Billing City */}
              <FormField name="billingCity" render={({ field }) => (
                <FormItem>
                  <Typography text="City" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="Johannesburg" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Billing State */}
              <FormField name="billingState" render={({ field }) => (
                <FormItem>
                  <Typography text="State/Province" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="Gauteng" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Billing Zip */}
              <FormField name="billingZip" render={({ field }) => (
                <FormItem>
                  <Typography text="ZIP / Postal Code" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="2001" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>

              {/* Billing Country (span both columns) */}
              <FormField name="billingCountry" render={({ field }) => (
                <FormItem  className="sm:col-span-2">
                  <Typography text="Country" variant="p" className="text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input placeholder="South Africa" {...field} className="dark:bg-gray-700 dark:text-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}/>
            </div>

            <Button
              variant="secondary"
              className="bg-blue-600 hover:bg-blue-500 w-full my-5 text-white"
              type="submit"
              disabled={isAuthenticating}
            >
              {isAuthenticating ? 'Signing up...' : 'Sign up'}
            </Button>
          </form>
        </Form>

        <div className="flex justify-center items-center gap-1 text-sx">
          <Typography text="Already have an account? " variant="p" />
          <Link href="/auth/login" className="underline text-blue-600">Sign in</Link>
        </div>
      </div>
    </div>
  );
};

export default Signup;
