"use client"

import { cn } from '@/lib/utils'
import { useTheme } from 'next-themes'
import React, { FC, ReactNode } from 'react'

const  MainContent: FC<{children: ReactNode}> = ({ children }) => {

    const { theme } = useTheme()

  return (
    <div className={cn('md:px-2 md:pt-14 md:pb-2 md:h-screen')}>
      <main className={cn('md:ml-[280px]  lg:ml-[420px] md:h-full overflow-y-hidden ',
        theme === 'dark'? 'bg-[#232529]' : 'bg-white'
      )}>
      {children}
      </main>
    </div>
  )
}

export default MainContent