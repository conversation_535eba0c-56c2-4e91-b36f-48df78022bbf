/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  <PERSON>, CardHeader, CardTitle, CardContent
} from "@/components/ui/card";
import {
  Table, TableHeader, TableRow, TableHead, TableBody, TableCell
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,  SelectContent,  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { getQuotationById, updateQuotation } from "@/query/quotes";

interface QuotationItem {
  id: number;
  item: string;
  description?: string;
  quantity: number;
  rate: number;
  tax?: number;
  discount?: number;
}

interface PaymentSchedule {
  dueDate: string;
  amount: number;
  note?: string;
}

interface CustomColumns {
  rate: string;
  item: string;
  description: string;
  quantity: string;
}

interface QuotationData {
  id: string;
  company_id: string;
  reference_number: string;
  quotation_date: string;
  expiry_date?: string;
  shipping_address: string;
  notes?: string;
  signature?: string;
  discount: number;
  tax: number;
  status: string;
  quotation_items: QuotationItem[];
  custom_columns: CustomColumns;
  payment_schedule: PaymentSchedule[];
  totals: {
    sub_total: number;
    total_tax: number;
    total_after_discount: number;
  };
  client?: {
    full_name: string;
    email: string;
    company_name: string;
  };
  is_converted_to_invoice: boolean;
}

export default function EditQuotationPage() {
  const router = useRouter();
  const params = useParams();
  const quotationId = (params as any)?.quotationId || (params as any)?.id;

  const [quotation, setQuotation] = useState<QuotationData | null>(null);
  const [loading, setLoading] = useState(true);

  // Form state for every schema field
  const [companyId, setCompanyId] = useState("");
  const [referenceNumber, setReferenceNumber] = useState("");
  const [quotationDate, setQuotationDate] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [shippingAddress, setShippingAddress] = useState("");
  const [notes, setNotes] = useState("");
  const [signature, setSignature] = useState("");
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [status, setStatus] = useState("Draft");
  const [items, setItems] = useState<QuotationItem[]>([]);
  const [customCols, setCustomCols] = useState<CustomColumns>({
    rate: "Rate",
    item: "Item",
    description: "Description",
    quantity: "Quantity",
  });
  const [paymentSchedule, setPaymentSchedule] = useState<PaymentSchedule[]>([]);
  const [isConverted, setIsConverted] = useState(false);

  // Fetch existing quotation
  useEffect(() => {
    async function load() {
      if (!quotationId) return setLoading(false);
      const { data, error } = await getQuotationById(quotationId as string);
      if (error) {
        toast.error("Error fetching quotation");
      } else {
        setQuotation(data);
        // populate form state
        setCompanyId(data.company_id);
        setReferenceNumber(data.reference_number);
        setQuotationDate(data.quotation_date);
        setExpiryDate(data.expiry_date || "");
        setShippingAddress(data.shipping_address);
        setNotes(data.notes || "");
        setSignature(data.signature || "");
        setDiscount(data.discount);
        setTax(data.tax);
        setStatus(data.status);
        setItems(data.quotation_items);
        setCustomCols(data.custom_columns);
        setPaymentSchedule(data.payment_schedule);
        setIsConverted(data.is_converted_to_invoice);
      }
      setLoading(false);
    }
    load();
  }, [quotationId]);

  // Totals recalculated on the fly
  const subTotal = items.reduce((s, i) => s + i.rate * i.quantity, 0);
  const totalTax = (subTotal * tax) / 100;
  const totalAfterDiscount = subTotal + totalTax - discount;
  const grandTotal = quotation?.totals.total_after_discount || totalAfterDiscount;

  // Handlers for dynamic rows
  const handleItemChange = (idx: number, field: keyof QuotationItem, val: string | number) => {
    const updated = [...items];
    updated[idx] = {
      ...updated[idx],
      [field]: typeof val === "string" ? parseFloat(val) || "" : val,
    };
    setItems(updated);
  };
  const addItem = () => {
    setItems(prev => [...prev, {
      id: Date.now(),
      item: "",
      description: "",
      quantity: 1,
      rate: 0,
    }]);
  };
  const removeItem = (id: number) => {
    setItems(prev => prev.filter(i => i.id !== id));
  };

  const handleScheduleChange = (idx: number, field: keyof PaymentSchedule, val: string | number) => {
    const updated = [...paymentSchedule];
    updated[idx] = {
      ...updated[idx],
      [field]: field === "dueDate" ? val as string : Number(val),
    };
    setPaymentSchedule(updated);
  };
  const addSchedule = () => {
    setPaymentSchedule(prev => [...prev, { dueDate: "", amount: 0, note: "" }]);
  };
  const removeSchedule = (index: number) => {
    setPaymentSchedule(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpdate = async () => {
    if (!quotation) return;
    const payload = {
      company_id: companyId,
      reference_number: referenceNumber,
      quotation_date: quotationDate,
      expiry_date: expiryDate,
      shipping_address: shippingAddress,
      notes,
      signature,
      discount,
      tax,
      status,
      quotation_items: items,
      custom_columns: customCols,
      payment_schedule: paymentSchedule,
      is_converted_to_invoice: isConverted,
    };
    const { error } = await updateQuotation(quotation.id, payload);
    if (error) toast.error("Failed to update: " + error.message);
    else {
      toast.success("Quotation updated");
      router.back();
    }
  };

  if (loading) return <p>Loading...</p>;
  if (!quotation) return <p>No quotation found.</p>;

  const client = quotation.client || { full_name: "", email: "", company_name: "" };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Edit Quotation</h1>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-1" /> Back
        </Button>
      </div>

      {/* Quotation Properties */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Company & Status */}
        <Card>
          <CardHeader><CardTitle>General</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Company ID</label>
              <Input value={companyId} onChange={e => setCompanyId(e.target.value)} />
            </div>
            <div>
  <label className="block text-sm font-medium">Status</label>
  <Select value={status} onValueChange={setStatus}>
    <SelectTrigger className="w-full">
      <SelectValue placeholder="Select status" />
    </SelectTrigger>
    <SelectContent>
      {["Draft", "pending", "approved", "rejected"].map((s) => (
        <SelectItem key={s} value={s}>
          {s}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
</div>


            <div>
              <label>Signature</label>
              <Input
                placeholder="e.g. John Doe"
                value={signature}
                onChange={e => setSignature(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              
                <label>Converted to Invoice</label>
                <input
                  type="checkbox"
                  checked={isConverted}
                  onChange={e => setIsConverted(e.target.checked)}
                />
              
            </div>
          </CardContent>
        </Card>

        {/* Reference & Dates */}
        <Card>
          <CardHeader><CardTitle>Reference & Dates</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Quotation #</label>
              <Input
                value={referenceNumber}
                onChange={e => setReferenceNumber(e.target.value)}
              />
            </div>
            <div>
              <label>Quotation Date</label>
              <Input
                type="date"
                value={quotationDate}
                onChange={e => setQuotationDate(e.target.value)}
              />
            </div>
            <div>
              <label>Expiry Date</label>
              <Input
                type="date"
                value={expiryDate}
                onChange={e => setExpiryDate(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Shipping & Notes */}
        <Card>
          <CardHeader><CardTitle>Shipping & Notes</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Shipping Address</label>
              <Input
                value={shippingAddress}
                onChange={e => setShippingAddress(e.target.value)}
              />
            </div>
            <div>
              <label>Notes</label>
              <Input
                value={notes}
                onChange={e => setNotes(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Items */}
      <Card>
        <CardHeader><CardTitle>Items</CardTitle></CardHeader>
        <CardContent>
          <div className="overflow-auto mb-2">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{customCols.item}</TableHead>
                  <TableHead>{customCols.description}</TableHead>
                  <TableHead>{customCols.quantity}</TableHead>
                  <TableHead>{customCols.rate}</TableHead>
                  <TableHead>Tax %</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((it, i) => {
                  const amt = it.quantity * it.rate;
                  return (
                    <TableRow key={it.id}>
                      <TableCell>
                        <Input
                          value={it.item}
                          onChange={e => handleItemChange(i, "item", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          value={it.description}
                          onChange={e => handleItemChange(i, "description", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.quantity}
                          onChange={e => handleItemChange(i, "quantity", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.rate}
                          onChange={e => handleItemChange(i, "rate", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.tax || 0}
                          onChange={e => handleItemChange(i, "tax", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={it.discount || 0}
                          onChange={e => handleItemChange(i, "discount", e.target.value)}
                        />
                      </TableCell>
                      <TableCell>R{amt.toFixed(2)}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline" onClick={() => removeItem(it.id)}>
                          Remove
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
          <Button variant="outline" onClick={addItem}>+ Add Item</Button>
        </CardContent>
      </Card>

      {/* Payment Schedule */}
      <Card>
        <CardHeader><CardTitle>Payment Schedule</CardTitle></CardHeader>
        <CardContent>
          <div className="overflow-auto mb-2">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Note</TableHead>
                  <TableHead>Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paymentSchedule.map((ps, i) => (
                  <TableRow key={i}>
                    <TableCell>
                      <Input
                        type="date"
                        value={ps.dueDate}
                        onChange={e => handleScheduleChange(i, "dueDate", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={ps.amount}
                        onChange={e => handleScheduleChange(i, "amount", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        value={ps.note}
                        onChange={e => handleScheduleChange(i, "note", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" onClick={() => removeSchedule(i)}>
                        Remove
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <Button variant="outline" onClick={addSchedule}>+ Add Schedule</Button>
        </CardContent>
      </Card>

      {/* Totals & Tax/Discount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader><CardTitle>Tax & Discount</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label>Tax %</label>
              <Input
                type="number"
                value={tax}
                onChange={e => setTax(parseFloat(e.target.value) || 0)}
              />
            </div>
            <div>
              <label>Discount</label>
              <Input
                type="number"
                value={discount}
                onChange={e => setDiscount(parseFloat(e.target.value) || 0)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader><CardTitle>Totals</CardTitle></CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Sub-total:</span>
              <span>R{subTotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Tax:</span>
              <span>R{totalTax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>After Discount:</span>
              <span>R{totalAfterDiscount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Grand Total:</span>
              <span>R{grandTotal.toFixed(2)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client Info */}
      <Card>
        <CardHeader><CardTitle>Bill To</CardTitle></CardHeader>
        <CardContent>
          <p className="font-semibold">{client.full_name}</p>
          <p>{shippingAddress}</p>
          <p>Email: {client.email}</p>
        </CardContent>
      </Card>

      {/* Save */}
      <div className="flex justify-end">
        <Button onClick={handleUpdate}>Update Quotation</Button>
      </div>
    </div>
  );
}
