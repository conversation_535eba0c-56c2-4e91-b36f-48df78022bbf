/* eslint-disable react-hooks/exhaustive-deps */
"use client"

import * as React from "react";
import { useRouter, usePathname } from "next/navigation";
import { ChevronsUpDown, Plus } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Company, User } from "@/types/app";
import { BsSlack } from "react-icons/bs";
import { Skeleton } from "@/components/ui/skeleton";

export function CompanySwitcher({
    company,
    user,
}: {
    company: Company[];
    user: User | null;
}) {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const pathname = usePathname(); 

  
  const userCompanyIds = Array.isArray(user?.company_id) ? user.company_id : [];

  
  const userCompanies = company.filter((c) => userCompanyIds.includes(c.id)) || [];

  
  const [activeTeam, setActiveTeam] = React.useState<Company | null>(null);

  
  React.useEffect(() => {
    const companyIdFromUrl = pathname.split("/").pop(); 
    const selectedCompany = userCompanies.find((c) => c.id === companyIdFromUrl);

    if (selectedCompany) {
      setActiveTeam(selectedCompany);
    } else if (userCompanies.length > 0 && !activeTeam) {
      setActiveTeam(userCompanies[0]); 
    }
  }, [pathname, userCompanies]);

  
  const handleSelectCompany = (selectedCompany: Company) => {
    setActiveTeam(selectedCompany);
    router.push(`/dashboard/${selectedCompany.id}`);
  };

  
  const isCEO = user?.role === "CEO";

  
  const canSeeDropdown = (user?.role === "Employee" || user?.role === "Client") && userCompanyIds.length > 1;

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            {activeTeam ? (
              (isCEO || canSeeDropdown) ? (
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <div className="flex aspect-square size-8 items-center justify-center rounded-lg text-sidebar-primary-foreground">
                    <BsSlack size={30} className="text-blue-500" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {activeTeam?.name || "Select Company"}
                    </span>
                    <span className="truncate text-xs">{activeTeam?.industry || "No industry"}</span>
                  </div>
                  <ChevronsUpDown className="ml-auto" />
                </SidebarMenuButton>
              ) : (
                <div className="flex items-center space-x-4 p-2">
                  <BsSlack size={30} className="text-blue-500" />
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {activeTeam?.name || "Select Company"}
                    </span>
                    <span className="truncate text-xs">{activeTeam?.industry || "No industry"}</span>
                  </div>
                </div>
              )
            ) : (
              // Show Skeleton only if `activeTeam` is still null after checking all cases
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              </SidebarMenuButton>
            )}
          </DropdownMenuTrigger>

          {isCEO || canSeeDropdown ? (
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              align="start"
              side={isMobile ? "bottom" : "right"}
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-xs text-muted-foreground">
                Choose your company
              </DropdownMenuLabel>

              {userCompanies.map((team, index) => (
                <DropdownMenuItem
                  key={team.id}
                  onClick={() => handleSelectCompany(team)}
                  className="gap-2 p-2 cursor-pointer"
                >
                  <div className="flex size-6 items-center justify-center rounded-sm border">
                    <BsSlack size={20} className="text-blue-500" />
                  </div>
                  {team.name}
                  <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                </DropdownMenuItem>
              ))}

              <DropdownMenuSeparator />

              {isCEO && (
                <DropdownMenuItem
                  className="gap-2 p-2 cursor-pointer"
                  onClick={() => router.push("/register-company")}
                >
                  <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                    <Plus className="size-4" />
                  </div>
                  <div className="font-medium text-muted-foreground">Create company</div>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          ) : null}
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
