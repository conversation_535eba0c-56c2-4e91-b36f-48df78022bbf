"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Plus, Minus, Trash } from "lucide-react";

export default function POSSalesPage() {
  const [invoiceItems, setInvoiceItems] = useState<{ name: string; price: number; quantity: number }[]>([]);
  const [newItemName, setNewItemName] = useState("");
  const [newItemPrice, setNewItemPrice] = useState(0);
  const [newItemQuantity, setNewItemQuantity] = useState(1);
  const [taxRate, setTaxRate] = useState(10);
  const [discount, setDiscount] = useState(0);
  const [selectedEmployee, setSelectedEmployee] = useState("");

  const addItem = () => {
    if (!newItemName || newItemPrice <= 0 || newItemQuantity <= 0) return;

    setInvoiceItems([
      ...invoiceItems,
      { name: newItemName, price: newItemPrice, quantity: newItemQuantity },
    ]);
    setNewItemName("");
    setNewItemPrice(0);
    setNewItemQuantity(1);
  };

  const removeItem = (index: number) => {
    setInvoiceItems(invoiceItems.filter((_, i) => i !== index));
  };

  const calculateSubtotal = () => {
    return invoiceItems.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  const calculateTax = () => {
    return (calculateSubtotal() * taxRate) / 100;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax() - discount;
  };

  const handleCreateInvoice = () => {
    const invoice = {
      items: invoiceItems,
      taxRate,
      discount,
      total: calculateTotal(),
      employee: selectedEmployee,
    };
    console.log("Invoice Created:", invoice);
    alert("Invoice created successfully!");
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>POS Sales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Add Item Form */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label>Item Name</Label>
                <Input
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  placeholder="Item Name"
                />
              </div>
              <div>
                <Label>Price</Label>
                <Input
                  type="number"
                  value={newItemPrice}
                  onChange={(e) => setNewItemPrice(Number(e.target.value))}
                  placeholder="Price"
                />
              </div>
              <div>
                <Label>Quantity</Label>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setNewItemQuantity((prev) => Math.max(1, prev - 1))}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Input
                    type="number"
                    value={newItemQuantity}
                    onChange={(e) => setNewItemQuantity(Number(e.target.value))}
                    className="w-20"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setNewItemQuantity((prev) => prev + 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            <Button onClick={addItem}>
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>

            {/* Invoice Items Table */}
            <div className="space-y-4">
              <h3 className="font-medium">Invoice Items</h3>
              {invoiceItems.map((item, index) => (
                <div key={index} className="flex justify-between items-center p-2 border rounded">
                  <span>{item.name}</span>
                  <span>${item.price} x {item.quantity}</span>
                  <Button size="sm" variant="destructive" onClick={() => removeItem(index)}>
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Tax, Discount, and Total */}
            <div className="space-y-2">
              <div>
                <Label>Tax Rate (%)</Label>
                <Input
                  type="number"
                  value={taxRate}
                  onChange={(e) => setTaxRate(Number(e.target.value))}
                />
              </div>
              <div>
                <Label>Discount ($)</Label>
                <Input
                  type="number"
                  value={discount}
                  onChange={(e) => setDiscount(Number(e.target.value))}
                />
              </div>
              <div>
                <Label>Subtotal</Label>
                <p>${calculateSubtotal()}</p>
              </div>
              <div>
                <Label>Tax</Label>
                <p>${calculateTax()}</p>
              </div>
              <div>
                <Label>Total</Label>
                <p>${calculateTotal()}</p>
              </div>
            </div>

            {/* Select Employee */}
            <div>
              <Label>Select Employee</Label>
              <Select onValueChange={setSelectedEmployee}>
                <SelectTrigger>
                  <SelectValue placeholder="Select employee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="employee-1">John Doe</SelectItem>
                  <SelectItem value="employee-2">Jane Smith</SelectItem>
                  <SelectItem value="employee-3">Mike Johnson</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Create Invoice Button */}
            <Button onClick={handleCreateInvoice}>Create Invoice</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}