"use client"; // Required for client-side interactivity

import { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";

import { Checkbox } from "@/components/ui/checkbox";

export default function RetainersPage() {
  const [client, setClient] = useState("");
  const [retainerStartDate, setRetainerStartDate] = useState<Date | null>(null);
  const [retainerPeriod, setRetainerPeriod] = useState("monthly");
  const [feesPerPeriod, setFeesPerPeriod] = useState(0);
  const [hoursPerPeriod, setHoursPerPeriod] = useState(0);
  const [description, setDescription] = useState("");
  const [notes, setNotes] = useState("");
  const [terms, setTerms] = useState("");
  const [acceptOnlinePayments, setAcceptOnlinePayments] = useState(false);
  const [invoiceTemplate, setInvoiceTemplate] = useState("default");
  const [invoiceColor, setInvoiceColor] = useState("#000000");
  const [invoiceFont, setInvoiceFont] = useState("Arial");
  const [nextIssueDate, setNextIssueDate] = useState<Date | null>(null);
  const [numberOfInvoices, setNumberOfInvoices] = useState("infinite");
  const [invoiceDelivery, setInvoiceDelivery] = useState("automatic");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const retainerData = {
      client,
      retainerStartDate,
      retainerPeriod,
      feesPerPeriod,
      hoursPerPeriod,
      description,
      notes,
      terms,
      acceptOnlinePayments,
      invoiceTemplate,
      invoiceColor,
      invoiceFont,
      nextIssueDate,
      numberOfInvoices,
      invoiceDelivery,
    };
    console.log("Retainer Submitted:", retainerData);
    alert("Retainer set up successfully!");
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Set Up Your Retainer</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Client Section */}
            <div>
              <label className="block font-medium mb-1">Client</label>
              <Input
                type="text"
                value={client}
                onChange={(e) => setClient(e.target.value)}
                placeholder="Type to add a client"
              />
            </div>

            {/* Retainer Terms */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block font-medium mb-1">Retainer Start Date</label>
                <DatePicker selected={retainerStartDate} onChange={(date: Date | undefined) => setRetainerStartDate(date ?? null)} />
              </div>
              <div>
                <label className="block font-medium mb-1">Retainer Period</label>
                <Select value={retainerPeriod} onValueChange={setRetainerPeriod}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Retainer Period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block font-medium mb-1">Fees Per Period</label>
                <Input
                  type="number"
                  value={feesPerPeriod}
                  onChange={(e) => setFeesPerPeriod(Number(e.target.value))}
                  placeholder="Enter fees per period"
                />
              </div>
              <div>
                <label className="block font-medium mb-1">Hours Per Period</label>
                <Input
                  type="number"
                  value={hoursPerPeriod}
                  onChange={(e) => setHoursPerPeriod(Number(e.target.value))}
                  placeholder="Enter hours per period"
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block font-medium mb-1">Description</label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Add a description (optional)"
              />
            </div>

            {/* Notes and Terms */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block font-medium mb-1">Notes</label>
                <Textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Enter notes (optional)"
                />
              </div>
              <div>
                <label className="block font-medium mb-1">Terms</label>
                <Textarea
                  value={terms}
                  onChange={(e) => setTerms(e.target.value)}
                  placeholder="Enter terms or conditions (optional)"
                />
              </div>
            </div>

            {/* Online Payments */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="online-payments"
                checked={acceptOnlinePayments}
                onCheckedChange={(checked) => setAcceptOnlinePayments(!!checked)}
              />
              <label htmlFor="online-payments" className="text-sm font-medium leading-none">
                Accept Online Payments
              </label>
            </div>

            {/* Invoice Customization */}
            <div className="border rounded p-4 space-y-4">
              <h3 className="text-lg font-bold">Customize Invoice Style</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium">Template</label>
                  <Select value={invoiceTemplate} onValueChange={setInvoiceTemplate}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Template" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default</SelectItem>
                      <SelectItem value="modern">Modern</SelectItem>
                      <SelectItem value="classic">Classic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium">Color</label>
                  <Input
                    type="color"
                    value={invoiceColor}
                    onChange={(e) => setInvoiceColor(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium">Font</label>
                  <Select value={invoiceFont} onValueChange={setInvoiceFont}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Font" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Arial">Arial</SelectItem>
                      <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                      <SelectItem value="Courier New">Courier New</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Recurring Schedule */}
            <div className="border rounded p-4 space-y-4">
              <h3 className="text-lg font-bold">Recurring Schedule</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium">Next Issue Date</label>
                  <DatePicker selected={nextIssueDate} onChange={(date: Date | undefined) => setNextIssueDate(date ?? null)} />
                </div>
                <div>
                  <label className="block text-sm font-medium">Number of Invoices</label>
                  <Select value={numberOfInvoices} onValueChange={setNumberOfInvoices}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Number of Invoices" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="infinite">Infinite</SelectItem>
                      <SelectItem value="specific">A specific number</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium">Invoice Delivery Options</label>
                <Select value={invoiceDelivery} onValueChange={setInvoiceDelivery}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Delivery Option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="automatic">Send invoices automatically</SelectItem>
                    <SelectItem value="manual">Create draft invoices and send manually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button type="submit">Set Up Retainer</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}