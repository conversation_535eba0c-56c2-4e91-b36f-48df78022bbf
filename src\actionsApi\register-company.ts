'use server';
import { supabaseServerClient } from '@/supabase/supabaseServer';

export async function registerCompany({
    ceoId,
    companyData,
    employeedIds,
}: {
    ceoId: string;
    employeedIds: string[];
    companyData: {
        name: string;
        industry: string;
        country: string;
        city: string;
        address: string;
        currency: string;
        yearsInBusiness: string;
        subscriptionOption: string;
        numberOfUsers: number;
        cellNumber: string;
        language: string;
        skip: boolean;
    };
}) {
    const supabase = await supabaseServerClient();

    try {
        
        const { data: company, error: companyError } = await supabase
            .from('companies')
            .insert([
                {
                    name: companyData.name,
                    industry: companyData.industry,
                    country: companyData.country,
                    city: companyData.city,
                    address: companyData.address,
                    currency: companyData.currency,
                    years_in_business: companyData.yearsInBusiness,
                    language: companyData.language,
                    phone: companyData.cellNumber,
                    number_of_users: companyData.numberOfUsers,
                    ceo_id: ceoId,
                    employees: [],
                    clients: [],
                    subscription_type: companyData.subscriptionOption,
                },
            ])
            .select('id')
            .single();

        if (companyError) {
            console.error('Error registering company:', companyError.message);
            return { error: companyError.message };
        }

        const companyId = company.id;

        
        const { data: ceoData, error: ceoFetchError } = await supabase
            .from('users')
            .select('company_id')
            .eq('id', ceoId)
            .single();

        if (ceoFetchError) {
            console.error('Error fetching CEO company_id:', ceoFetchError.message);
            return { error: ceoFetchError.message };
        }

        const updatedCompanyIds = ceoData?.company_id ? [...new Set([...ceoData.company_id, companyId])] : [companyId];

        
        const { error: ceoUpdateError } = await supabase
            .from('users')
            .update({ company_id: updatedCompanyIds })
            .eq('id', ceoId);

        if (ceoUpdateError) {
            console.error('Error updating CEO company_id:', ceoUpdateError.message);
            return { error: ceoUpdateError.message };
        }

        console.log("Skip option", companyData.skip);
        console.log("Employees id", employeedIds);

        if (!companyData.skip) {
            for (const employeeId of employeedIds) {
                
                const { data: employeeData, error: employeeFetchError } = await supabase
                    .from('users')
                    .select('company_id')
                    .eq('id', employeeId)
                    .single();

                if (employeeFetchError) {
                    console.error(`Error fetching employee company_id for ID ${employeeId}:`, employeeFetchError.message);
                    return { error: employeeFetchError.message };
                }

                const updatedEmployeeCompanyIds = employeeData?.company_id ? [...new Set([...employeeData.company_id, companyId])] : [companyId];

                
                const { error: employeeUpdateError } = await supabase
                    .from('users')
                    .update({ company_id: updatedEmployeeCompanyIds, supervisor_id: ceoId })
                    .eq('id', employeeId);

                if (employeeUpdateError) {
                    console.error(`Error updating employee in users table with ID ${employeeId}:`, employeeUpdateError.message);
                    return { error: `Error updating employee in users table with ID ${employeeId}: ${employeeUpdateError.message}` };
                }

                
                const { data: empData, error: empFetchError } = await supabase
                    .from('employees')
                    .select('company_id')
                    .eq('id', employeeId)
                    .maybeSingle();

                if (empFetchError) {
                    console.error(`Error fetching employee company_id in employees table for ID ${employeeId}:`, empFetchError.message);
                    return { error: empFetchError.message };
                }

                const updatedEmployeeCompanyIdsEmpTable = empData?.company_id ? [...new Set([...empData.company_id, companyId])] : [companyId];

                const { error: empUpdateError } = await supabase
                    .from('employees')
                    .update({ company_id: updatedEmployeeCompanyIdsEmpTable, supervisor_id: ceoId })
                    .eq('id', employeeId);

                if (empUpdateError) {
                    console.error(`Error updating employee in employees table with ID ${employeeId}:`, empUpdateError.message);
                    return { error: `Error updating employee in employees table with ID ${employeeId}: ${empUpdateError.message}` };
                }

                console.log(`Successfully updated employee with ID ${employeeId}`);
            }

            
            const { error: updateEmployeesError } = await supabase
                .from('companies')
                .update({ employees: employeedIds })
                .eq('id', companyId);

            if (updateEmployeesError) {
                console.error('Error updating employees array in companies table:', updateEmployeesError.message);
                return { error: updateEmployeesError.message };
            }
        }

        return { success: true, companyId };
    } catch (error) {
        console.error('Unexpected error during company registration:', error);
        return { error: 'An unexpected error occurred during company registration.' };
    }
}

export type NewEmployee = {
    fullName:    string;
    email:       string;
    password:    string;
    phone:       string;
    gender:      string;
    street:      string;
    city:        string;
    state:       string;
    zip:         string;
    country:     string;
  };
  
  export async function registerEmployees({
    employees,
  }: {
    employees: NewEmployee[];
  }) {
    const supabase = await supabaseServerClient();
    const registered: Array<{ id: string; email: string }> = [];
  
    try {
      for (const emp of employees) {
        
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: emp.email,
            password: emp.password,
            options:{
                data: {
                    data: {
                        fullName:  emp.fullName,
                        role:      'Employee',
                        phone:     emp.phone,
                        gender:  emp.gender,
                        billingStreet:  emp.street,
                        billingCity:    emp.city,
                        billingState:   emp.state,
                        billingZip:     emp.zip,
                        billingCountry: emp.country,
                      },
                }
            }
        }
        );
  
        if (authError) {
          if (
            authError.status === 400 &&
            authError.message.includes('User already registered')
          ) {
            return {
              error: `User ${emp.email} already exists – please log in instead.`,
            };
          }
          console.error(`Auth error for ${emp.email}:`, authError.message);
          return { error: authError.message };
        }
  
        const userId = authData.user?.id;
        if (!userId) {
          const msg = `Failed to retrieve auth ID for ${emp.email}`;
          console.error(msg);
          return { error: msg };
        }
  
        registered.push({ id: userId, email: emp.email });
      }
  
      return { success: true, employees: registered };
    } catch (err) {
      console.error('Unexpected error registering employees:', err);
      return {
        error:
          'An unexpected error occurred during employees registration.',
      };
    }
  }

