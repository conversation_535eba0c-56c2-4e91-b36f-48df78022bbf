import StepperLayout from '@/components/stepperSignUp/stepper-layout';
import React from 'react';
import { getUserData } from '@/actionsApi/get-user';

async function RegisterCompany() {
    const userData = await getUserData();
    if (!userData) {
        return (
            <div className="flex justify-center items-center min-h-screen bg-gray-200 dark:bg-gray-900">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-blue-500 border-b-2 dark:border-blue-300"></div>
            </div>
        );
    }
    return (
        <div>
            <StepperLayout userData={userData} />
        </div>
    );
}

export default RegisterCompany;
