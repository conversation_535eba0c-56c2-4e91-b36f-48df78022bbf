/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
// /query/quotations.ts
import { supabaseBrowserClient } from "@/supabase/supbaseClient";
export type APIResponse<T> = { data?: T; error?: any };

export interface QuotationData {
  clientId: string;
  companyId: string;
  quotationItems: {
    id: number;
    item: string;
    description: string;
    quantity: number;
    rate: number;
  }[];
  tax: number;
  discount: number;
  shippingAddress: string;
  notes: string;
  signature: string;
  attachment?: File | null;
  customColumns: {
    rate: string;
    item: string;
    description: string;
    quantity: string;
  };
  paymentSchedule: { dueDate: string; amount: number; note: string }[];
  isDraft: boolean;
  totals: {
    subTotal: number;
    totalTax: number;
    totalAfterDiscount: number;
  };
  bankingDetails: {
    bankName: string;
    branchCode: string;
    accountNumber: string;
  };
}

export async function createQuotation(quotationData: QuotationData) {
  const supabase = supabaseBrowserClient;

  const dataToInsert = {
    client_id: quotationData.clientId,
    company_id: quotationData.companyId,
    quotation_items: quotationData.quotationItems,
    tax: quotationData.tax,
    discount: quotationData.discount,
    shipping_address: quotationData.shippingAddress,
    notes: quotationData.notes,
    signature: quotationData.signature,
    attachment: quotationData.attachment ? quotationData.attachment.name : null,
    custom_columns: quotationData.customColumns,
    payment_schedule: quotationData.paymentSchedule,
    status: 'pending',
    is_draft: quotationData.isDraft,
    is_converted_to_invoice: false,
    totals: {
      sub_total: quotationData.totals.subTotal,
      total_tax: quotationData.totals.totalTax,
      total_after_discount: quotationData.totals.totalAfterDiscount,
    },
    bank_name: quotationData.bankingDetails.bankName,
    branch_code: quotationData.bankingDetails.branchCode,
    account_number: quotationData.bankingDetails.accountNumber,
  };

  const { data, error } = await supabase
    .from("quotations")
    .insert([dataToInsert]);

  if (error) {
    return { error };
  }
  return { data };
}


export async function getAllQuotations(companyId: string) {
  const supabase = supabaseBrowserClient;

  const { data, error } = await supabase
    .from("quotations")
    .select("*, clients(full_name)")
    .eq("company_id", companyId)

  if (error) {
    return { error };
  }
  return { data };
}

export async function getQuotationById(quotationId: string) {
  const supabase = supabaseBrowserClient;

  const { data, error } = await supabase
    .from("quotations")
    .select("*, clients(full_name, email, billing_street, billing_city), companies(name)")
    .eq("id", quotationId)
    .single();

  if (error) {
    return { error };
  }
  return { data };
}

export async function updateQuotationStatus(quotationId: string, newStatus: string): Promise<APIResponse<any>> {
  const supabase = supabaseBrowserClient;
  // Update only the status and updated_at fields
  const { data, error } = await supabase
    .from("quotations")
    .update({
      status: newStatus,
      updated_at: new Date().toISOString(),
    })
    .eq("id", quotationId)
    .single();
  return { data, error };
}

export async function convertQuotationToInvoice(quotationId: string): Promise<APIResponse<{ id: string }>> {
  const supabase = supabaseBrowserClient;
  
  // Fetch the complete quotation data
  const { data: quotationData, error: fetchError } = await supabase
    .from("quotations")
    .select("*")
    .eq("id", quotationId)
    .single();
  if (fetchError) {
    console.error("Error fetching quotation:", fetchError);
    return { error: fetchError };
  }
  
  // Check if the quotation has already been converted
  if (quotationData.is_converted_to_invoice) {
    console.warn("Quotation already converted to invoice");
    return { error: { message: "Quotation already converted to invoice" } };
  }
  
  // Update the quotation to mark it as converted
  const { data: updatedQuotation, error: updateError } = await supabase
    .from("quotations")
    .update({
      is_converted_to_invoice: true,
      updated_at: new Date().toISOString(),
    })
    .eq("id", quotationId)
    .single();
  if (updateError) {
    console.error("Error updating quotation:", updateError);
    return { error: updateError };
  }

  // Prepare the invoice payload by copying all data from the quotation
  const invoicePayload = {
    ...quotationData,
    is_converted_to_invoice: true,
    updated_at: new Date().toISOString(),
  };

  // Log the payload for debugging purposes
  console.log("Invoice Payload:", invoicePayload);

  // Insert the payload into the invoices table and retrieve the inserted row
  const { data: invoiceData, error: invoiceError } = await supabase
    .from("invoices")
    .insert([invoicePayload])
    .select()
    .single();
  if (invoiceError) {
    console.error("Error inserting invoice:", invoiceError);
    return { error: invoiceError };
  }

  return { data: invoiceData };
}

export async function updateQuotation(quotationId: string, updatedData: any): Promise<APIResponse<any>> {
  const supabase = supabaseBrowserClient;
  const { data, error } = await supabase
    .from("quotations")
    .update({
      ...updatedData,
      updated_at: new Date().toISOString(),
    })
    .eq("id", quotationId)
    .single();
  return { data, error };
}
