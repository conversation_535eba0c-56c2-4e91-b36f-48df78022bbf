"use client"; // Required for client-side interactivity

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search,  Edit, Trash } from "lucide-react";

export default function ManageRetainersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Sample data for demonstration
  const retainers = [
    {
      id: 1,
      client: "Client A",
      nextInvoiceDate: "2023-11-01",
      remainingInvoices: 5,
      feePerPeriod: 1000,
      totalRevenue: 5000,
      status: "active",
    },
    {
      id: 2,
      client: "Client B",
      nextInvoiceDate: "2023-10-15",
      remainingInvoices: 2,
      feePerPeriod: 1500,
      totalRevenue: 3000,
      status: "pending",
    },
    {
      id: 3,
      client: "Client C",
      nextInvoiceDate: "2023-12-01",
      remainingInvoices: 10,
      feePerPeriod: 2000,
      totalRevenue: 20000,
      status: "completed",
    },
  ];

  const filteredRetainers = retainers.filter((retainer) => {
    const matchesSearch = retainer.client.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || retainer.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDeleteRetainer = (id: number) => {
    // Implement delete functionality
    console.log("Delete retainer with id:", id);
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Manage Retainers</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search by client name"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Retainers Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Next Invoice / Remaining</TableHead>
                <TableHead>Fee / Period</TableHead>
                <TableHead>Total Revenue</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRetainers.map((retainer) => (
                <TableRow key={retainer.id}>
                  <TableCell>{retainer.client}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{retainer.nextInvoiceDate}</span>
                      <span className="text-sm text-muted-foreground">
                        {retainer.remainingInvoices} remaining
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>${retainer.feePerPeriod}</TableCell>
                  <TableCell>${retainer.totalRevenue}</TableCell>
                  <TableCell>
                    <Badge variant={retainer.status === "active" ? "default" : "secondary"}>
                      {retainer.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteRetainer(retainer.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}