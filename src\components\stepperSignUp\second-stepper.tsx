
import { useSignupValues } from '@/hooks/signup-values';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Typography from '../ui/typography';

const formSchema = z.object({
    yearsInBusiness: z.string().nonempty("Please select an option"),
});

const yearsOptions = [
    "Less than a year",
    "1 Year",
    "2 Years",
    "3 Years",
    "4 Years",
    "5 Years and more",
];

const SecondStepper = () => {
    const { setCurrentStep, updateFormData, formData } = useSignupValues();

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: formData,
    });

    const handleNext = (data: z.infer<typeof formSchema>) => {
        updateFormData(data);
        setCurrentStep(3);
    };

    const handleBack = () => {
        setCurrentStep(1);
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleNext)}>
                <Typography
                    text="Step 2: Years of your Business Operation"
                    variant="h2"
                    className="text-2xl font-bold text-blue-600 dark:text-blue-400"
                />
                <Typography
                    text="Please select the number of years your business has been running."
                    variant="p"
                    className="opacity-90 mb-7 text-gray-600 dark:text-gray-400 text-center"
                />
                <FormField
                    name="yearsInBusiness"
                    render={({ field }) => (
                        <FormItem>
                            <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">How long has your company been running?</label>
                            <FormControl>
                                <select
                                    {...field}
                                    className="w-full dark:bg-gray-700 dark:text-white border border-gray-300 rounded-lg p-2"
                                >
                                    <option value="">Select an Option</option>
                                    {yearsOptions.map((option) => (
                                        <option key={option} value={option}>
                                            {option}
                                        </option>
                                    ))}
                                </select>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <div className="flex justify-between mt-6">
                    <Button type="button" onClick={handleBack} className="bg-gray-400 hover:bg-gray-300 text-white">
                        Back
                    </Button>
                    <Button type="submit" className="bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 hover:bg-blue-500 dark:text-white text-white">
                        Next
                    </Button>
                </div>
            </form>
        </Form>
    );
};

export default SecondStepper;
