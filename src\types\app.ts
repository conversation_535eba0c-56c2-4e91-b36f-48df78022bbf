export type User = {
    avatar_url: string | null
    company_id: string[] | null
    created_at: string | null
    email: string | null
    full_name: string | null
    id: string
    role: string
    supervisor_id: string | null
    updated_at: string | null
}

export type Company = {
    address: string | null
    ceo_id: string | null
    city: string | null
    clients: string[] | null
    country: string | null
    created_at: string | null
    currency: string | null
    employees: string[] | null
    id: string
    industry: string | null
    language: string | null
    name: string
    number_of_users: number | null
    phone: string | null
    subscription_status: string | null
    subscription_type: string | null
    updated_at: string | null
    years_in_business: string | null
}