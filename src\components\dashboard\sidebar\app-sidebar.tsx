/* eslint-disable @typescript-eslint/no-unused-vars */
"use client"

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { useUserProfile } from "@/query/user";
import { CompanySwitcher } from "./company-switcher";
import { useUserCompanies } from "@/query/get-company";
import { INDUSTRY_SIDEBARS } from "@/config/sidebar-config";
import { User, Company as UserCompany } from "@/types/app";
import { Skeleton } from "@/components/ui/skeleton";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const params = useParams();
  const companyId = params?.companyId as string;
  const { profile, company, loading, error } = useUserProfile(currentUser?.id, companyId);
  const { loading: companyLoading, companies } = useUserCompanies(profile?.company_id ?? []);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const response = await fetch("/api/getUser");
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

        const data = await response.json();
        

        if (data.success) {
          setCurrentUser(data.user.user);
        } else {
          console.error("User fetch error:", data.error);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      }
    };

    fetchUser();
  }, []);

  
  const companyIndustry = company?.industry || null;

  
  const sidebarMenu = companyIndustry && INDUSTRY_SIDEBARS[companyIndustry]
    ? INDUSTRY_SIDEBARS[companyIndustry]
    : null;

  return (
    <Sidebar className="bg-gray-100 dark:bg-gray-900" collapsible="icon" {...props}>
      <SidebarHeader>
        {companyLoading ? (
          <div className="animate-pulse h-10 w-full bg-gray-300 dark:bg-gray-700 rounded-md"></div>
        ) : (
          <CompanySwitcher company={companies as UserCompany[]} user={profile} />
        )}
      </SidebarHeader>

      <SidebarContent>
        {companyLoading || loading ? (
          <div className="mt-5 ml-5 space-y-4">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-6 w-6 rounded-md" />
                <Skeleton className="h-4 w-36" />
              </div>
            ))}
          </div>
        ) : sidebarMenu ? (
          <NavMain items={sidebarMenu}   />
        ) : null}
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={profile} loading={loading} />
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
