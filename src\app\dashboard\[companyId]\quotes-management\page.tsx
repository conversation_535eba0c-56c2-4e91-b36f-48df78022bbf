/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Plus } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { getAllQuotations } from "@/query/quotes";

type Quote = {
  id: string;
  reference_number: string;
  created_at: string;
  total_amount: number;
  status: string;
  client: string;
};

export default function QuotesManagementPage() {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const params = useParams();
  const companyId = params?.companyId as string;

  useEffect(() => {
    async function fetchQuotes() {
      const result = await getAllQuotations(companyId);
      if (result.error) {
        console.error("Error fetching quotations:", result.error);
      } else {
        const transformedQuotes: Quote[] = result.data.map((q: any) => ({
          id: q.id,
          reference_number: q.reference_number,
          created_at: q.created_at,
          total_amount: q.totals?.total_after_discount || 0,
          status: q.status,
          client: q.clients?.full_name || "Unknown Client",
        }));
        setQuotes(transformedQuotes);
      }
    }
    fetchQuotes();
  }, []);

  const filteredQuotes = quotes.filter((quote) =>
    quote.reference_number.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto p-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0"></h1>
        <Link href={`/dashboard/${companyId}/new-quotes`} as={`/dashboard/${companyId}/new-quotes`}>
          <Button className="flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            <span>New Quote</span>
          </Button>
        </Link>
      </div>

      {/* Search and Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-center">
            <CardTitle className="mb-4 md:mb-0">Quotes Management</CardTitle>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                placeholder="Search by reference number..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full sm:w-64"
              />
              <Button className="flex items-center">
                <Search className="h-4 w-4 mr-2" />
                <span>Search</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No.</TableHead>
                  <TableHead>Quote Number</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Quote Date</TableHead>
                  <TableHead>Total Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredQuotes.length > 0 ? (
                  filteredQuotes.map((quote, index) => {
                    const formattedDate = new Date(quote.created_at).toLocaleDateString("en-US", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    });
                    return (
                      <TableRow key={quote.id}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>{quote.reference_number}</TableCell>
                        <TableCell>{quote.client}</TableCell>
                        <TableCell>{formattedDate}</TableCell>
                        <TableCell>R{quote.total_amount}</TableCell>
                        <TableCell>
                        <Badge
                          className={
                          quote.status === "approved"
                            ? "bg-green-600 text-white"
                            : quote.status === "rejected"
                            ? "bg-red-600 text-white"
                            : quote.status === "pending"
                            ? "bg-yellow-600 text-white"
                            : "bg-gray-100 text-gray-800"
                          }
                          >
                          {quote.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Link
                            href={`/dashboard/${companyId}/quotes-management/view/${quote.id}`}
                            as={`/dashboard/${companyId}/quotes-management/view/${quote.id}`}
                          >
                            <Button size="sm" variant="outline">
                              View
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center">
                      No record found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
