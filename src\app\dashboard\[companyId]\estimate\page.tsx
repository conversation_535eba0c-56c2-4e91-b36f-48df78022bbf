"use client";

import React, { useState, useMemo, useEffect } from "react";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useParams } from "next/navigation";
import { useUserProfile } from "@/query/user";
import { fetchClientsByCompanyId } from "@/query/clients";
import { createQuotation, QuotationData } from "@/query/quotes";
import { toast } from "sonner";
import { User } from "@/types/app";

interface QuotationItem {
  id: number;
  item: string;
  description: string;
  quantity: number;
  rate: number;
}

export default function CreateQuotationPage() {
  const [selectedClient, setSelectedClient] = useState<string>("");
  const [clientList, setClientList] = useState<Array<{ id: string; full_name: string }>>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [start, setStarting] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const params = useParams();
  const companyId = params?.companyId as string;
  const { loading } = useUserProfile(currentUser?.id, companyId);

  const [tax, setTax] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [shippingAddress, setShippingAddress] = useState("");
  const [notes, setNotes] = useState("");
  const [signature, setSignature] = useState("");
  const [attachment, setAttachment] = useState<File | null>(null);
  const [isDraft, setIsDraft] = useState(false);
  const [isCustomizeOpen, setIsCustomizeOpen] = useState(false);
  const [bankName, setBankName] = useState("");
  const [branchCode, setBranchCode] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  
  const [customRateLabel, setCustomRateLabel] = useState("Amount");
  const [customItemLabel, setCustomItemLabel] = useState("Item");
  const [customDescriptionLabel, setCustomDescriptionLabel] = useState("Description");
  const [customQuantityLabel, setCustomQuantityLabel] = useState("Quantity");

  const [paymentDueDate, setPaymentDueDate] = useState("");
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentNote, setPaymentNote] = useState("");
  const [paymentSchedule, setPaymentSchedule] = useState<{ dueDate: string; amount: number; note: string }[]>([]);

  const [quotationItems, setQuotationItems] = useState<QuotationItem[]>([
    { id: 1, item: "", description: "", quantity: 1, rate: 0 },
  ]);

  const subTotal = useMemo(() => quotationItems.reduce((acc, item) => acc + item.quantity * item.rate, 0), [quotationItems]);
  const totalTax = useMemo(() => (subTotal * tax) / 100, [subTotal, tax]);
  const totalAfterDiscount = useMemo(() => subTotal + totalTax - discount, [subTotal, totalTax, discount]);

  const handleAddSignature = () => {
    setSignature("E-Signature: Client Signed");
  };

  const handleSaveDraft = () => {
    setIsDraft(true);
    alert("Quotation saved as draft. You can continue editing later.");
  };

  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setAttachment(e.target.files[0]);
    }
  };

  const handleAddPaymentSchedule = () => {
    if (!paymentDueDate || paymentAmount <= 0) return;
    setPaymentSchedule((prev) => [
      ...prev,
      { dueDate: paymentDueDate, amount: paymentAmount, note: paymentNote },
    ]);
    setPaymentDueDate("");
    setPaymentAmount(0);
    setPaymentNote("");
  };

  const handleAddQuotationItem = () => {
    setQuotationItems((prev) => [
      ...prev,
      { id: prev.length + 1, item: "", description: "", quantity: 1, rate: 0 },
    ]);
  };

  const handleRemoveQuotationItem = (id: number) => {
    setQuotationItems((prev) => prev.filter((item) => item.id !== id));
  };

  const handleQuotationItemChange = (id: number, field: keyof QuotationItem, value: string | number) => {
    setQuotationItems((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, [field]: typeof value === "string" ? value : Number(value) } : item
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const quotationData: QuotationData = {
      clientId: selectedClient,
      companyId, 
      quotationItems,
      tax,
      discount,
      shippingAddress,
      notes,
      signature,
      attachment,
      customColumns: {
        rate: customRateLabel,
        item: customItemLabel,
        description: customDescriptionLabel,
        quantity: customQuantityLabel,
      },
      paymentSchedule,
      isDraft,
      totals: {
        subTotal,
        totalTax,
        totalAfterDiscount,
      },
      bankingDetails: {        // ← added here
        bankName,
        branchCode,
        accountNumber,
      },
    };
    setSubmitting(true);
    const result = await createQuotation(quotationData);
    setSubmitting(false);
    if (result.error) {
      console.error("Error creating quotation:", result.error);
      toast.error("Failed to create quotation!");
    } else {
      console.log("Quotation created:", result.data);
      toast.success("Quotation submitted successfully!");
    }
  };

  useEffect(() => {
    async function fetchUser() {
      try {
        const response = await fetch("/api/getUser");
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        const data = await response.json();
        if (data.success) {
          setCurrentUser(data.user.user);
        } else {
          console.error("User fetch error:", data.error);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      } finally {
        setStarting(false);
      }
    }
    async function getClients() {
      const result = await fetchClientsByCompanyId(companyId);
      if (result.error) {
        console.error("Error fetching clients:", result.error);
      } else {
        setClientList(result.data ?? []);
      }
    }
    if (companyId) {
      getClients();
    }
    fetchUser();
  }, [companyId]);

  if (loading || start) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 border-solid"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Logo at the top */}
     
      <Card>
        <CardHeader>
          <CardTitle>New Quotation</CardTitle>
        </CardHeader>
        <div className="flex justify-center mb-4">
        <img src="/logo.png" alt="Logo" className="h-12" />
      </div>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Client Selection */}
            <div>
              <label className="block font-medium mb-1">Select Client</label>
              <Select value={selectedClient} onValueChange={(val) => setSelectedClient(val)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Choose a client" />
                </SelectTrigger>
                <SelectContent>
                  {clientList.length > 0 ? (
                    clientList.map((client) => (
                      <SelectItem key={client.id} value={client.id}>
                        {client.full_name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="">No Clients Found</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Button to open customization dialog */}
            <Button type="button" variant="outline" onClick={() => setIsCustomizeOpen(true)}>
              Customize Columns
            </Button>

            {/* Dialog for customizing columns */}
            <Dialog open={isCustomizeOpen} onOpenChange={setIsCustomizeOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Customize Columns</DialogTitle>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <label className="block text-sm font-medium">Rate Label</label>
                    <Input value={customRateLabel} onChange={(e) => setCustomRateLabel(e.target.value)} placeholder="Rate" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">Item Label</label>
                    <Input value={customItemLabel} onChange={(e) => setCustomItemLabel(e.target.value)} placeholder="Item" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">Description Label</label>
                    <Input value={customDescriptionLabel} onChange={(e) => setCustomDescriptionLabel(e.target.value)} placeholder="Description" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium">Quantity Label (or Weight)</label>
                    <Input value={customQuantityLabel} onChange={(e) => setCustomQuantityLabel(e.target.value)} placeholder="Quantity or Weight" />
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-6">
                  <Button variant="outline" onClick={() => setIsCustomizeOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsCustomizeOpen(false)}>Save</Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* Quotation Items */}
            <div className="border p-4 space-y-4">
              <h3 className="text-lg font-bold">Items</h3>
              {quotationItems.map((item) => (
                <div key={item.id} className="grid grid-cols-6 gap-2">
                  <div className="col-span-1">
                    <label className="text-sm font-medium">{customItemLabel}</label>
                    <Input
                      value={item.item}
                      onChange={(e) => handleQuotationItemChange(item.id, "item", e.target.value)}
                      placeholder="Item"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="text-sm font-medium">{customDescriptionLabel}</label>
                    <Input
                      value={item.description}
                      onChange={(e) => handleQuotationItemChange(item.id, "description", e.target.value)}
                      placeholder="Description"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">{customQuantityLabel}</label>
                    <Input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => handleQuotationItemChange(item.id, "quantity", e.target.value)}
                      placeholder="1"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">{customRateLabel}</label>
                    <Input
                      type="number"
                      value={item.rate}
                      onChange={(e) => handleQuotationItemChange(item.id, "rate", e.target.value)}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button type="button" variant="outline" onClick={() => handleRemoveQuotationItem(item.id)}>
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" onClick={handleAddQuotationItem}>
                + Add a Line
              </Button>
            </div>

            {/* Tax and Discount */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block font-medium mb-1">Tax (%)</label>
                <Input type="number" value={tax} onChange={(e) => setTax(Number(e.target.value))} placeholder="Enter tax percentage" />
              </div>
              <div>
                <label className="block font-medium mb-1">Discount ($)</label>
                <Input type="number" value={discount} onChange={(e) => setDiscount(Number(e.target.value))} placeholder="Enter discount amount" />
              </div>
            </div>

            {/* Shipping Address */}
            <div>
              <label className="block font-medium mb-1">Shipping Address</label>
              <Input value={shippingAddress} onChange={(e) => setShippingAddress(e.target.value)} placeholder="Enter shipping address" />
            </div>

            {/* Notes */}
            <div>
              <label className="block font-medium mb-1">Notes</label>
              <textarea className="w-full border rounded p-2" rows={3} value={notes} onChange={(e) => setNotes(e.target.value)} placeholder="Add any notes here" />
            </div>

            {/* Attachment */}
            <div>
              <label className="block font-medium mb-1">Attachment</label>
              <Input type="file" onChange={handleAttachmentChange} />
              {attachment && <p className="text-sm mt-1">Selected: {attachment.name}</p>}
            </div>
            {/* Banking Details */}
<div className="grid grid-cols-3 gap-4">
  <div>
    <label className="block font-medium mb-1">Bank Name</label>
    <Input
      value={bankName}
      onChange={e => setBankName(e.target.value)}
      placeholder="e.g. First National Bank"
    />
  </div>
  <div>
    <label className="block font-medium mb-1">Branch Code</label>
    <Input
      value={branchCode}
      onChange={e => setBranchCode(e.target.value)}
      placeholder="e.g. 250655"
    />
  </div>
  <div>
    <label className="block font-medium mb-1">Account Number</label>
    <Input
      value={accountNumber}
      onChange={e => setAccountNumber(e.target.value)}
      placeholder="e.g. **********"
    />
  </div>
</div>


            {/* Payment Schedule */}
            <div className="border rounded p-4 space-y-4">
              <h3 className="text-lg font-bold">Payment Schedule</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium">Due Date</label>
                  <Input type="date" value={paymentDueDate} onChange={(e) => setPaymentDueDate(e.target.value)} />
                </div>
                <div>
                  <label className="block text-sm font-medium">Amount</label>
                  <Input type="number" value={paymentAmount} onChange={(e) => setPaymentAmount(Number(e.target.value))} placeholder="Amount" />
                </div>
                <div>
                  <label className="block text-sm font-medium">Note</label>
                  <Input value={paymentNote} onChange={(e) => setPaymentNote(e.target.value)} placeholder="Payment note" />
                </div>
              </div>
              <Button type="button" onClick={handleAddPaymentSchedule}>Add Payment Schedule Item</Button>
              {paymentSchedule.length > 0 && (
                <ul className="mt-2 space-y-1">
                  {paymentSchedule.map((item, index) => (
                    <li key={index} className="text-sm">
                      Due: {item.dueDate} - ${item.amount} {item.note && `(${item.note})`}
                    </li>
                  ))}
                </ul>
              )}
            </div>


            {/* E-Signature */}
            <div className="flex items-center gap-4">
              <Button type="button" onClick={handleAddSignature}>Add E-Signature</Button>
              {signature && <span className="italic text-green-600">{signature}</span>}
            </div>

            {/* Totals */}
            <div className="space-y-2 border rounded p-4">
              <p>Subtotal: ${subTotal.toFixed(2)}</p>
              <p>Tax: ${totalTax.toFixed(2)}</p>
              <p>Discount: ${discount.toFixed(2)}</p>
              <p className="font-semibold">Total: ${totalAfterDiscount.toFixed(2)}</p>
            </div>

            {/* Submit */}
            <div className="flex gap-4">
              <Button type="button" variant="outline" onClick={() => { setIsDraft(true); handleSaveDraft(); }}>
                Save as Draft
              </Button>
              <Button type="submit" disabled={submitting}>{submitting ? "Submitting..." : "Submit Quotation"}</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
