"use client";

import { useState } from "react";
import { useRouter } from "next/navigation"; // Import useRouter
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function NotesAndMeetingsPage() {
  const router = useRouter(); // Initialize useRouter

  const [notes, setNotes] = useState<{ id: string; title: string; content: string }[]>([]);
  const [meetings, setMeetings] = useState<{ id: string; client: string; title: string; date: string; time: string; notes: string }[]>([]);

  const [noteTitle, setNoteTitle] = useState("");
  const [noteContent, setNoteContent] = useState("");

  const [meetingClient, setMeetingClient] = useState("");
  const [meetingTitle, setMeetingTitle] = useState("");
  const [meetingDate, setMeetingDate] = useState("");
  const [meetingTime, setMeetingTime] = useState("");
  const [meetingNotes, setMeetingNotes] = useState("");

  const handleAddNote = () => {
    if (!noteTitle.trim() && !noteContent.trim()) return;
    const newNote = {
      id: Date.now().toString(),
      title: noteTitle.trim(),
      content: noteContent.trim(),
    };
    setNotes((prev) => [newNote, ...prev]);
    setNoteTitle("");
    setNoteContent("");
  };

  const handleDeleteNote = (id: string) => {
    setNotes((prev) => prev.filter((note) => note.id !== id));
  };

  const handleAddMeeting = () => {
    if (!meetingClient.trim() || !meetingTitle.trim() || !meetingDate || !meetingTime) return;
    const newMeeting = {
      id: Date.now().toString(),
      client: meetingClient.trim(),
      title: meetingTitle.trim(),
      date: meetingDate,
      time: meetingTime,
      notes: meetingNotes.trim(),
    };
    setMeetings((prev) => [newMeeting, ...prev]);
    setMeetingClient("");
    setMeetingTitle("");
    setMeetingDate("");
    setMeetingTime("");
    setMeetingNotes("");
  };

  const handleDeleteMeeting = (id: string) => {
    setMeetings((prev) => prev.filter((meeting) => meeting.id !== id));
  };

  return (
    <div className="container mx-auto p-4 space-y-10">
      {/* Top Right Button */}
      <div className="flex justify-end mb-4">
        <Button
          //className="bg-blue-500 hover:bg-blue-600 text-white"
          onClick={() => router.push("/dashboard/$(companyId)/client-meeting-management")} // Navigate to the manage meetings page
        >
          Manage Meetings
        </Button>
      </div>

      {/* Meeting Management Section */}
      <Card>
        <CardHeader>
          <CardTitle>Meeting</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Client Name</label>
            <Input value={meetingClient} onChange={(e) => setMeetingClient(e.target.value)} placeholder="Client Name" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Meeting Title</label>
            <Input value={meetingTitle} onChange={(e) => setMeetingTitle(e.target.value)} placeholder="Meeting Title" />
          </div>
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Date</label>
              <Input type="date" value={meetingDate} onChange={(e) => setMeetingDate(e.target.value)} />
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Time</label>
              <Input type="time" value={meetingTime} onChange={(e) => setMeetingTime(e.target.value)} />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Notes (optional)</label>
            <textarea className="w-full border rounded p-2" rows={3} value={meetingNotes} onChange={(e) => setMeetingNotes(e.target.value)} placeholder="Meeting notes..." />
          </div>
          <Button onClick={handleAddMeeting}>Add Meeting</Button>
        </CardContent>
      </Card>

      {meetings.length > 0 && (
        <div className="space-y-4">
          {meetings.map((meeting) => (
            <Card key={meeting.id}>
              <CardHeader className="flex justify-between items-center">
                <CardTitle>{meeting.title} with {meeting.client}</CardTitle>
                <Button variant="destructive" size="sm" onClick={() => handleDeleteMeeting(meeting.id)}>
                  Delete
                </Button>
              </CardHeader>
              <CardContent>
                <p><strong>Date:</strong> {meeting.date}</p>
                <p><strong>Time:</strong> {meeting.time}</p>
                {meeting.notes && <p className="mt-2">{meeting.notes}</p>}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}