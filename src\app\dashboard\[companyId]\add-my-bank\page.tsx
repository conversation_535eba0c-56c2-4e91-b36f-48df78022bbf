"use client";

import React, { useState } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function BankAccountPage() {
  const [bankFile, setBankFile] = useState<File | null>(null);

  const handleAddBankAccount = () => {
    // Implement logic to add a bank account (e.g., open a form/modal)
    alert("Redirecting to bank account addition form...");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setBankFile(e.target.files[0]);
    }
  };

  const handleReconcileExpenses = () => {
    // Implement bank reconciliation with expenses
    alert("Reconciling bank statement with expenses...");
  };

  const handleReconcileIncomes = () => {
    // Implement bank reconciliation with incomes/invoices
    alert("Reconciling bank statement with incomes/invoices...");
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Add Bank Account Section */}
      <Card>
        <CardHeader>
          <CardTitle>Add Your Bank Account</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Click to add your Bank Account (To view Statement &amp; Upload in the system)
          </p>
          <Button onClick={handleAddBankAccount}>Add Bank Account</Button>
        </CardContent>
      </Card>

      {/* Import Bank Statement Section */}
      <Card>
        <CardHeader>
          <CardTitle>Import Bank Statement</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">Import your Bank Statement (CSV File)</p>
          <Input type="file" accept=".csv" onChange={handleFileChange} />
          {bankFile && (
            <p className="mt-2 text-sm text-gray-600">Selected File: {bankFile.name}</p>
          )}
        </CardContent>
      </Card>

      {/* Bank Reconciliation Section */}
      <Card>
        <CardHeader>
          <CardTitle>Bank Reconciliation</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">Reconcile your bank statement with your Expenses and Incomes/Invoices</p>
          <div className="flex flex-col gap-4">
            <Button onClick={handleReconcileExpenses}>
              Reconcile with Expenses
            </Button>
            <Button onClick={handleReconcileIncomes} variant="outline">
              Reconcile with Incomes/Invoices
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
