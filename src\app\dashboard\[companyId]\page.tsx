
"use client"
import React, { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { CarouselDemo } from "@/components/dashboard/carousel/carousel"
import { ResizablePanelDemo } from "@/components/dashboard/resizable-panel/resizable-panel"
import Typography from "@/components/ui/typography"
import { ChartComponent } from "@/components/dashboard/charts/chart1"
import { Chart2Component } from "@/components/dashboard/charts/chart2"
import { TabsDemo } from "@/components/dashboard/tabs/tabs"
import { DataTableDemo } from "@/components/dashboard/table/data-table"
import { User } from "@/types/app"
import { useUserProfile } from "@/query/user"


const DashboardPage =  () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [start, setStarting] = useState(true);
  const params = useParams(); 
  const companyId = params?.companyId as string; 

  const {  company, loading } = useUserProfile(currentUser?.id, companyId);

  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const response = await fetch("/api/getUser");
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log("Trying", data);

        if (data.success) {
          setCurrentUser(data.user.user);
        } else {
          console.error("User fetch error:", data.error);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      } finally {
        setStarting(false);
      }
    };

    fetchUser();
  }, []);

  console.log(company);

  
  if (loading || start) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 border-solid"></div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen p-4 md:p-6 bg-gray-200 dark:bg-gray-900">
      {/* Header Section */}
      <div className="bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-6 text-center">
        <div className="flex flex-wrap justify-center items-center gap-2">
          <Typography variant="h1" className="text-2xl font-bold text-gray-800 dark:text-white" text="👋 Welcome To" />
          <div className="flex">
            <Typography text={company?.name || "Your Company"} className="text-2xl font-bold text-blue-500" variant="h1" />
          </div>
          <Typography variant="h1" className="text-2xl font-bold text-gray-800 dark:text-white" text="System" />
        </div>
        <Typography variant="h3" className="text-lg mt-2 text-gray-600 dark:text-gray-300" text="Manage your customer relationships efficiently and effectively." />
      </div>

      {/* Resizable Panel - Alone in its own Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div className="lg:col-span-2 bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-4">
          <ResizablePanelDemo />
        </div>
      </div>

      {/* Grouping Components into Two Columns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        {/* Carousel & Chart 1 */}
        <div className="bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-2">
          <CarouselDemo />
        </div>
        <div className="bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-4 flex items-center justify-center">
          <div className="w-full ">
            <Chart2Component />
          </div>
        </div>

        {/* Chart 2 & Tabs */}
        <div className="bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-4 flex items-center justify-center">
          <div className="w-full ">
            <ChartComponent />
          </div>
        </div>
        <div className="bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-4">
          <TabsDemo />
        </div>

        {/* Data Table Alone */}
        <div className="lg:col-span-2 bg-gray-100 dark:bg-gray-800 shadow-md rounded-lg p-4">
          <DataTableDemo />
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
