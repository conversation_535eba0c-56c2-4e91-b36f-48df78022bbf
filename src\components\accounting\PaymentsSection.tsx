import React, { useState } from 'react';
import { Payment } from '@/types/accounting';
import { formatCurrency } from './utils';
import { useToast } from '@/hooks/use-toast';

interface PaymentsSectionProps {
  // Only pass essential data and minimal callbacks
}

const PaymentsSection: React.FC<PaymentsSectionProps> = () => {
  const { toast } = useToast();

  // Move all payment-related state here
  const [payments, setPayments] = useState<Payment[]>([]);
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedPaymentIds, setSelectedPaymentIds] = useState<string[]>([]);
  const [selectAllPayments, setSelectAllPayments] = useState(false);
  const [showPaymentDetailsDialog, setShowPaymentDetailsDialog] = useState(false);
  const [showPaymentSettingsDialog, setShowPaymentSettingsDialog] = useState(false);
  const [showRefundDialog, setShowRefundDialog] = useState(false);
  const [showReceiptDialog, setShowReceiptDialog] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [paymentSearchTerm, setPaymentSearchTerm] = useState('');
  const [paymentFilterStatus, setPaymentFilterStatus] = useState<'all' | 'pending' | 'completed' | 'failed' | 'refunded'>('all');
  const [paymentFilterGateway, setPaymentFilterGateway] = useState<'all' | 'stripe' | 'paypal' | 'bank'>('all');
  const [paymentSortBy, setPaymentSortBy] = useState<'date' | 'amount' | 'payer'>('date');
  const [paymentSortOrder, setPaymentSortOrder] = useState<'asc' | 'desc'>('desc');

  const [paymentForm, setPaymentForm] = useState<{
    amount: string;
    currency: string;
    paymentMethod: string;
    payerName: string;
    payerEmail: string;
    description: string;
    invoiceId: string;
    gateway: string;
    reference: string;
  }>({
    amount: '',
    currency: 'ZAR',
    paymentMethod: 'credit_card',
    payerName: '',
    payerEmail: '',
    description: '',
    invoiceId: '',
    gateway: 'stripe',
    reference: ''
  });

  const [paymentSettings, setPaymentSettings] = useState({
    stripe: {
      enabled: false,
      publicKey: '',
      secretKey: '',
      webhookSecret: ''
    },
    paypal: {
      enabled: false,
      clientId: '',
      clientSecret: '',
      environment: 'sandbox' as const
    },
    bank: {
      enabled: false,
      accountName: '',
      accountNumber: '',
      bankName: '',
      branchCode: ''
    },
    fees: {
      creditCard: 2.9,
      bankTransfer: 0,
      paypal: 3.4
    },
    currency: {
      default: 'ZAR',
      supported: ['ZAR', 'USD', 'EUR', 'GBP']
    }
  });

  // Enhanced Payment CRUD Handlers
  const handleSavePayment = () => {
    // Form validation
    if (!paymentForm.amount.trim() || parseFloat(paymentForm.amount) <= 0) {
      toast({
        title: "Validation Error",
        description: "Payment amount must be greater than zero.",
        variant: "destructive",
      });
      return;
    }

    if (!paymentForm.payerName.trim()) {
      toast({
        title: "Validation Error",
        description: "Payer name is required.",
        variant: "destructive",
      });
      return;
    }

    if (!paymentForm.payerEmail.trim() || !paymentForm.payerEmail.includes('@')) {
      toast({
        title: "Validation Error",
        description: "Valid payer email is required.",
        variant: "destructive",
      });
      return;
    }

    if (!paymentForm.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Payment description is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      const amount = parseFloat(paymentForm.amount);
      const fees = amount * (paymentSettings.fees.creditCard / 100);
      const netAmount = amount - fees;

      if (editingPayment) {
        // Update existing payment
        const updatedPayments = payments.map(payment =>
          payment.id === editingPayment.id
            ? {
                ...payment,
                amount,
                currency: paymentForm.currency,
                paymentMethod: paymentForm.paymentMethod,
                payerName: paymentForm.payerName.trim(),
                payerEmail: paymentForm.payerEmail.trim(),
                description: paymentForm.description.trim(),
                invoiceId: paymentForm.invoiceId || undefined,
                gateway: paymentForm.gateway,
                fees,
                netAmount
              }
            : payment
        );
        setPayments(updatedPayments);
        toast({
          title: "Success",
          description: "Payment updated successfully!",
        });
      } else {
        // Create new payment
        const newPayment: Payment = {
          id: Date.now().toString(),
          paymentNumber: `PAY-2024-${String(payments.length + 1).padStart(3, '0')}`,
          date: new Date(),
          amount,
          currency: paymentForm.currency,
          paymentMethod: paymentForm.paymentMethod,
          payerName: paymentForm.payerName.trim(),
          payerEmail: paymentForm.payerEmail.trim(),
          description: paymentForm.description.trim(),
          invoiceId: paymentForm.invoiceId || undefined,
          status: 'pending',
          transactionId: `txn_${Date.now()}`,
          gateway: paymentForm.gateway,
          fees,
          netAmount
        };
        setPayments([...payments, newPayment]);
        toast({
          title: "Success",
          description: "Payment processed successfully!",
        });
      }

      // Reset form and close dialog
      setPaymentForm({
        amount: '',
        currency: 'ZAR',
        paymentMethod: 'credit_card',
        payerName: '',
        payerEmail: '',
        description: '',
        invoiceId: '',
        gateway: 'stripe',
        reference: ''
      });
      setEditingPayment(null);
      setShowPaymentDialog(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleViewPaymentDetails = (payment: Payment) => {
    setSelectedPayment(payment);
    setShowPaymentDetailsDialog(true);
  };

  const handleEditPayment = (payment: Payment) => {
    if (payment.status === 'completed' || payment.status === 'refunded') {
      toast({
        title: "Cannot Edit",
        description: "Cannot edit completed or refunded payments.",
        variant: "destructive",
      });
      return;
    }

    setEditingPayment(payment);
    setPaymentForm({
      amount: payment.amount.toString(),
      currency: payment.currency,
      paymentMethod: payment.paymentMethod,
      payerName: payment.payerName,
      payerEmail: payment.payerEmail,
      description: payment.description,
      invoiceId: payment.invoiceId || '',
      gateway: payment.gateway,
      reference: ''
    });
    setShowPaymentDialog(true);
  };

  const handleDeletePayment = (paymentId: string) => {
    const payment = payments.find(p => p.id === paymentId);
    if (!payment) return;

    if (payment.status === 'completed' || payment.status === 'refunded') {
      toast({
        title: "Cannot Delete",
        description: "Cannot delete completed or refunded payments.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to delete payment "${payment.paymentNumber}"?`)) {
      try {
        const updatedPayments = payments.filter(p => p.id !== paymentId);
        setPayments(updatedPayments);
        toast({
          title: "Success",
          description: "Payment deleted successfully!",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete payment. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleRefundPayment = (payment: Payment) => {
    if (payment.status !== 'completed') {
      toast({
        title: "Cannot Refund",
        description: "Only completed payments can be refunded.",
        variant: "destructive",
      });
      return;
    }

    setSelectedPayment(payment);
    setShowRefundDialog(true);
  };

  const handleSendReceipt = (payment: Payment) => {
    setSelectedPayment(payment);
    setShowReceiptDialog(true);
  };

  const getFilteredPayments = () => {
    const filtered = payments.filter(payment => {
      const matchesSearch = payment.paymentNumber.toLowerCase().includes(paymentSearchTerm.toLowerCase()) ||
                           payment.payerName.toLowerCase().includes(paymentSearchTerm.toLowerCase()) ||
                           payment.payerEmail.toLowerCase().includes(paymentSearchTerm.toLowerCase()) ||
                           payment.description.toLowerCase().includes(paymentSearchTerm.toLowerCase());
      const matchesStatus = paymentFilterStatus === 'all' || payment.status === paymentFilterStatus;
      const matchesGateway = paymentFilterGateway === 'all' || payment.gateway.toLowerCase() === paymentFilterGateway;

      return matchesSearch && matchesStatus && matchesGateway;
    });

    // Sort payments
    filtered.sort((a, b) => {
      let aValue, bValue;
      switch (paymentSortBy) {
        case 'date':
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
          break;
        case 'amount':
          aValue = a.amount;
          bValue = b.amount;
          break;
        case 'payer':
          aValue = a.payerName;
          bValue = b.payerName;
          break;
        default:
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return paymentSortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return paymentSortOrder === 'asc' ?
        (aValue as number) - (bValue as number) :
        (bValue as number) - (aValue as number);
    });

    return filtered;
  };

  const filteredPayments = getFilteredPayments();

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Payments</h2>
      <table className="min-w-full bg-white border">
        <thead>
          <tr>
            <th>Payment #</th>
            <th>Date</th>
            <th>Payer</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Gateway</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {filteredPayments.map(payment => (
            <tr key={payment.id}>
              <td>{payment.paymentNumber}</td>
              <td>{new Date(payment.date).toLocaleDateString()}</td>
              <td>{payment.payerName}</td>
              <td>{formatCurrency(payment.amount)}</td>
              <td>{payment.status}</td>
              <td>{payment.gateway}</td>
              <td>
                <button onClick={() => handleEditPayment(payment)}>Edit</button>
                <button onClick={() => handleDeletePayment(payment.id)}>Delete</button>
                <button onClick={() => handleRefundPayment(payment)}>Refund</button>
                <button onClick={() => handleSendReceipt(payment)}>Send Receipt</button>
                <button onClick={() => handleViewPaymentDetails(payment)}>Details</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* Add dialogs, bulk actions, filters, etc. as needed */}
    </div>
  );
};

export default PaymentsSection;