/* eslint-disable @next/next/no-img-element */
"use client";

import React, { useState, useMemo, useEffect, useCallback } from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useParams, useRouter } from "next/navigation";
import { useUserProfile } from "@/query/user";
import { fetchClientsByCompanyId } from "@/query/clients";
import { createInvoice, InvoiceData } from "@/query/invoices";
import { toast } from "sonner";
import { User } from "@/types/app";

interface InvoiceItem {
  id: number;
  item: string;
  description: string;
  quantity: number;
  rate: number;
}

export default function InvoiceManagementPage() {
  const router = useRouter();
  const { companyId } = useParams() as { companyId: string };
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const { loading } = useUserProfile(currentUser?.id, companyId);

  // Clients
  const [clientList, setClientList] = useState<{ id: string; full_name: string }[]>([]);
  const [selectedClient, setSelectedClient] = useState("");

  // Invoice items
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([
    { id: 1, item: "", description: "", quantity: 1, rate: 0 },
  ]);

  // Tax / discount
  const [tax, setTax] = useState(0);
  const [discount, setDiscount] = useState(0);

  // Shipping, notes, signature, attachment
  const [shippingAddress, setShippingAddress] = useState("");
  const [notes, setNotes] = useState("");
  const [signature, setSignature] = useState("");
  const [attachment, setAttachment] = useState<File | null>(null);

  // Draft flag
  const [isDraft, setIsDraft] = useState(false);

  // Static payment schedule
  const [paymentDueDate, setPaymentDueDate] = useState("");
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentNote, setPaymentNote] = useState("");

  // Banking details
  const [bankName, setBankName] = useState("");
  const [branchCode, setBranchCode] = useState("");
  const [accountNumber, setAccountNumber] = useState("");

  // Customize Columns dialog
  const [isCustomizeOpen, setIsCustomizeOpen] = useState(false);
  const [customItemLabel, setCustomItemLabel] = useState("Item");
  const [customDescriptionLabel, setCustomDescriptionLabel] = useState("Description");
  const [customQuantityLabel, setCustomQuantityLabel] = useState("Quantity");
  const [customRateLabel, setCustomRateLabel] = useState("Amount");

  // Submitting state
  const [submitting, setSubmitting] = useState(false);

  // Totals
  const subTotal = useMemo(
    () => invoiceItems.reduce((sum, i) => sum + i.quantity * i.rate, 0),
    [invoiceItems]
  );
  const totalTax = useMemo(() => (subTotal * tax) / 100, [subTotal, tax]);
  const totalAfterDiscount = useMemo(
    () => subTotal + totalTax - discount,
    [subTotal, totalTax, discount]
  );

  // Handlers for line items
  const handleAddLine = () =>
    setInvoiceItems((prev) => [
      ...prev,
      { id: prev.length + 1, item: "", description: "", quantity: 1, rate: 0 },
    ]);

  const handleRemoveLine = (id: number) =>
    setInvoiceItems((prev) => prev.filter((it) => it.id !== id));

  const handleLineChange = (
    id: number,
    field: keyof InvoiceItem,
    value: string | number
  ) => {
    setInvoiceItems((prev) =>
      prev.map((it) =>
        it.id === id
          ? { ...it, [field]: typeof value === "string" ? value : Number(value) }
          : it
      )
    );
  };

  // Reset form
  const resetForm = useCallback(() => {
    setSelectedClient("");
    setInvoiceItems([{ id: 1, item: "", description: "", quantity: 1, rate: 0 }]);
    setTax(0);
    setDiscount(0);
    setShippingAddress("");
    setNotes("");
    setSignature("");
    setAttachment(null);
    setIsDraft(false);
    setPaymentDueDate("");
    setPaymentAmount(0);
    setPaymentNote("");
    setBankName("");
    setBranchCode("");
    setAccountNumber("");
  }, []);

  // Simple validation
  const validateForm = (): boolean => {
    if (!selectedClient) return toast.error("Please select a client."), false;
    if (shippingAddress.trim() === "")
      return toast.error("Shipping address is required."), false;
    if (!bankName || !branchCode || !accountNumber)
      return toast.error("Please fill in all banking details."), false;
    return true;
  };

  // Submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const data: InvoiceData = {
      clientId: selectedClient,
      companyId,
      invoiceItems,
      tax,
      discount,
      shippingAddress,
      notes,
      signature,
      attachment,
      customColumns: {
        item: customItemLabel,
        description: customDescriptionLabel,
        quantity: customQuantityLabel,
        rate: customRateLabel,
      },
      paymentSchedule: [
        { dueDate: paymentDueDate, amount: paymentAmount, note: paymentNote },
      ],
      isDraft,
      totals: { subTotal, totalTax, totalAfterDiscount },
      bankingDetails: { bankName, branchCode, accountNumber },
    };

    setSubmitting(true);
    const result = await createInvoice(data);
    setSubmitting(false);

    if (result.error) {
      toast.error("Failed to create invoice.");
    } else {
      toast.success("Invoice created!");
      resetForm();
      router.push(`/dashboard/${companyId}/manage-invoice`);
    }
  };

  // Fetch user & clients
  useEffect(() => {
    fetch("/api/getUser")
      .then((r) => r.json())
      .then((d) => d.success && setCurrentUser(d.user.user))
      .catch(console.error);

    fetchClientsByCompanyId(companyId).then((res) => {
      if (res.data) setClientList(res.data);
    });
  }, [companyId]);

  if (loading) return <div className="p-6">Loading…</div>;

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-center">New Invoice</CardTitle>
        </CardHeader>
        <CardContent>
        <div className="flex mb-5">
            <img src="/logo.png" alt="Logo" className="h-12" />
          </div>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Client */}
            <div>
              <label className="block font-medium mb-1">Client</label>
              <Select
                value={selectedClient}
                onValueChange={setSelectedClient}
                required
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Choose a client" />
                </SelectTrigger>
                <SelectContent>
                  {clientList.map((c) => (
                    <SelectItem key={c.id} value={c.id}>
                      {c.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Customize Columns */}
            <div>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCustomizeOpen(true)}
              >
                Customize Columns
              </Button>
              <Dialog
                open={isCustomizeOpen}
                onOpenChange={setIsCustomizeOpen}
              >
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Customize Column Labels</DialogTitle>
                  </DialogHeader>
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm">Item Label</label>
                      <Input
                        value={customItemLabel}
                        onChange={(e) => setCustomItemLabel(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm">Description Label</label>
                      <Input
                        value={customDescriptionLabel}
                        onChange={(e) =>
                          setCustomDescriptionLabel(e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <label className="block text-sm">Quantity Label</label>
                      <Input
                        value={customQuantityLabel}
                        onChange={(e) =>
                          setCustomQuantityLabel(e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <label className="block text-sm">Rate Label</label>
                      <Input
                        value={customRateLabel}
                        onChange={(e) => setCustomRateLabel(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end mt-6 space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsCustomizeOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={() => setIsCustomizeOpen(false)}>
                      Save
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Items */}
            <div className="border p-4 space-y-4">
              <h3 className="text-lg font-bold">Items</h3>
              {invoiceItems.map((it) => (
                <div
                  key={it.id}
                  className="grid grid-cols-6 gap-2 items-end"
                >
                  <div>
                    <label className="block text-sm">
                      {customItemLabel}
                    </label>
                    <Input
                      value={it.item}
                      onChange={(e) =>
                        handleLineChange(it.id, "item", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm">
                      {customDescriptionLabel}
                    </label>
                    <Input
                      value={it.description}
                      onChange={(e) =>
                        handleLineChange(it.id, "description", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm">
                      {customQuantityLabel}
                    </label>
                    <Input
                      type="number"
                      min={1}
                      value={it.quantity}
                      onChange={(e) =>
                        handleLineChange(it.id, "quantity", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm">
                      {customRateLabel}
                    </label>
                    <Input
                      type="number"
                      min={0}
                      step={0.01}
                      value={it.rate}
                      onChange={(e) =>
                        handleLineChange(it.id, "rate", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handleRemoveLine(it.id)}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" onClick={handleAddLine}>
                + Add a Line
              </Button>
            </div>

            {/* Tax & Discount */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block">Tax (%)</label>
                <Input
                  type="number"
                  min={0}
                  value={tax}
                  onChange={(e) => setTax(Number(e.target.value))}
                />
              </div>
              <div>
                <label className="block">Discount</label>
                <Input
                  type="number"
                  min={0}
                  value={discount}
                  onChange={(e) => setDiscount(Number(e.target.value))}  
                />
              </div>
            </div>

            {/* Shipping Address */}
            <div>
              <label className="block font-medium mb-1">
                Shipping Address*
              </label>
              <Input
                value={shippingAddress}
                onChange={(e) => setShippingAddress(e.target.value)}
                placeholder="Enter address"
                required
              />
            </div>

            {/* Payment Schedule */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block">Due Date</label>
                <Input
                  type="date"
                  value={paymentDueDate}
                  onChange={(e) => setPaymentDueDate(e.target.value)}
                />
              </div>
              <div>
                <label className="block">Amount</label>
                <Input
                  type="number"
                  min={0}
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(Number(e.target.value))}
                />
              </div>
              <div>
                <label className="block">Note</label>
                <Input
                  value={paymentNote}
                  onChange={(e) => setPaymentNote(e.target.value)}
                />
              </div>
            </div>

            {/* Banking Details */}
            <div className="border rounded p-4 space-y-4">
              <h3 className="text-lg font-bold">Banking Details*</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block">Bank Name</label>
                  <Input
                    value={bankName}
                    onChange={(e) => setBankName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block">Branch Code</label>
                  <Input
                    value={branchCode}
                    onChange={(e) => setBranchCode(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block">Account #</label>
                  <Input
                    value={accountNumber}
                    onChange={(e) => setAccountNumber(e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            {/* Notes & Attachment */}
            <div>
              <label className="block font-medium mb-1">Notes</label>
              <textarea
                className="w-full border rounded p-2"
                rows={3}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
            <div>
              <label className="block font-medium mb-1">Attachment</label>
              <input
                className="block w-full border rounded p-2"
                type="file"
                onChange={(e) =>
                  e.target.files && setAttachment(e.target.files[0])
                }
              />
              {attachment && (
                <p className="text-sm mt-1">Selected: {attachment.name}</p>
              )}
            </div>

            {/* Totals & Submit */}
            <div className="space-y-2 border rounded p-4">
              <p>Subtotal: R{subTotal.toFixed(2)}</p>
              <p>Tax: R{totalTax.toFixed(2)}</p>
              <p>Discount: R{discount.toFixed(2)}</p>
              <p className="font-semibold">
                Total: R{totalAfterDiscount.toFixed(2)}
              </p>
            </div>
            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsDraft(true);
                  toast("Draft saved");
                }}
              >
                Save Draft
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? "Submitting..." : "Submit Invoice"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
