import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../ui/table';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '../ui/dropdown-menu';
import { Plus, Upload, BookOpen, Banknote, CheckCircle, MoreHorizontal, Trash2, Eye, Edit, XCircle } from 'lucide-react';

interface BankReconciliationProps {
  reconciliations: any[];
  selectedReconciliation: any;
  setSelectedReconciliation: (rec: any) => void;
  showReconciliationDialog: boolean;
  setShowReconciliationDialog: (show: boolean) => void;
  showImportStatementDialog: boolean;
  setShowImportStatementDialog: (show: boolean) => void;
  selectedBankTransaction: any;
  setSelectedBankTransaction: (tx: any) => void;
  selectedBookEntry: any;
  setSelectedBookEntry: (entry: any) => void;
  showMatchDialog: boolean;
  setShowMatchDialog: (show: boolean) => void;
  getUnmatchedBankTransactions: () => any[];
  getUnmatchedBookEntries: () => any[];
  handleStartReconciliation: () => void;
  handleImportBankStatement: (file: File) => void;
  handleMatchTransaction: (bankTransaction: any, bookEntry: any) => void;
  handleUnmatchTransaction: (bankTransaction: any) => void;
  handleCompleteReconciliation: (reconciliationId: string) => void;
  handleDeleteReconciliation: (reconciliationId: string) => void;
  handleCancelReconciliation: (reconciliationId: string) => void;
  formatCurrency: (amount: number, currency?: string) => string;
}

const BankReconciliation: React.FC<BankReconciliationProps> = ({
  reconciliations,
  selectedReconciliation,
  setSelectedReconciliation,
  showReconciliationDialog,
  setShowReconciliationDialog,
  showImportStatementDialog,
  setShowImportStatementDialog,
  selectedBankTransaction,
  setSelectedBankTransaction,
  selectedBookEntry,
  setSelectedBookEntry,
  showMatchDialog,
  setShowMatchDialog,
  getUnmatchedBankTransactions,
  getUnmatchedBookEntries,
  handleStartReconciliation,
  handleImportBankStatement,
  handleMatchTransaction,
  handleUnmatchTransaction,
  handleCompleteReconciliation,
  handleDeleteReconciliation,
  handleCancelReconciliation,
  formatCurrency
}) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Bank Reconciliation</h2>
        <div className="flex gap-2">
          <Button onClick={() => setShowReconciliationDialog(true)}>
            <Plus size={16} className="mr-2" />
            Start Reconciliation
          </Button>
          <Button variant="outline" onClick={() => setShowImportStatementDialog(true)}>
            <Upload size={16} className="mr-2" />
            Import Bank Statement
          </Button>
        </div>
      </div>

      {/* Reconciliation Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Banknote size={20} className="text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Bank Balance</p>
                <p className="text-xl font-bold">{formatCurrency(selectedReconciliation?.closingBalance || 87500)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BookOpen size={20} className="text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Book Balance</p>
                <p className="text-xl font-bold">{formatCurrency(selectedReconciliation?.bookBalance || 85000)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle size={20} className={`text-${selectedReconciliation?.difference === 0 ? 'green' : 'red'}-600`} />
              <div>
                <p className="text-sm text-gray-600">Difference</p>
                <p className="text-xl font-bold">{formatCurrency(selectedReconciliation?.difference || 2500)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Unreconciled Items */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Unmatched Bank Transactions ({getUnmatchedBankTransactions().length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getUnmatchedBankTransactions().length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No unmatched bank transactions
                </div>
              ) : (
                getUnmatchedBankTransactions().map((transaction) => (
                  <div key={transaction.id} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-gray-600">{transaction.date.toLocaleDateString()}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </p>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => {
                          setSelectedBankTransaction(transaction);
                          setShowMatchDialog(true);
                        }}
                      >
                        Match
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Unmatched Book Entries ({getUnmatchedBookEntries().length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getUnmatchedBookEntries().length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No unmatched book entries
                </div>
              ) : (
                getUnmatchedBookEntries().map((entry) => (
                  <div key={entry.id} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <p className="font-medium">{entry.description}</p>
                      <p className="text-sm text-gray-600">{entry.date.toLocaleDateString()}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${entry.totalDebit > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {entry.totalDebit > 0 ? '-' : '+'}{formatCurrency(entry.totalDebit || entry.totalCredit)}
                      </p>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => {
                          setSelectedBookEntry(entry);
                          setShowMatchDialog(true);
                        }}
                      >
                        Match
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reconciliation History Table */}
      <Card>
        <CardHeader>
          <CardTitle>Reconciliation History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Reconciliation #</TableHead>
                <TableHead>Bank Account</TableHead>
                <TableHead>Statement Date</TableHead>
                <TableHead>Bank Balance</TableHead>
                <TableHead>Book Balance</TableHead>
                <TableHead>Difference</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reconciliations.map((reconciliation) => (
                <TableRow key={reconciliation.id}>
                  <TableCell className="font-medium">{reconciliation.reconciliationNumber}</TableCell>
                  <TableCell>{reconciliation.bankAccount}</TableCell>
                  <TableCell>{reconciliation.statementDate.toLocaleDateString()}</TableCell>
                  <TableCell>{formatCurrency(reconciliation.closingBalance)}</TableCell>
                  <TableCell>{formatCurrency(reconciliation.bookBalance)}</TableCell>
                  <TableCell className={reconciliation.difference === 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatCurrency(reconciliation.difference)}
                  </TableCell>
                  <TableCell>{reconciliation.status}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal size={14} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => setSelectedReconciliation(reconciliation)}>
                          <Eye size={14} className="mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCompleteReconciliation(reconciliation.id)} disabled={reconciliation.status !== 'in_progress'}>
                          <CheckCircle size={14} className="mr-2" />
                          Complete
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCancelReconciliation(reconciliation.id)} disabled={reconciliation.status !== 'in_progress'}>
                          <XCircle size={14} className="mr-2" />
                          Cancel
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteReconciliation(reconciliation.id)}>
                          <Trash2 size={14} className="mr-2" />
                          Delete
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setSelectedReconciliation(reconciliation)}>
                          <Edit size={14} className="mr-2" />
                          Edit
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default BankReconciliation;
