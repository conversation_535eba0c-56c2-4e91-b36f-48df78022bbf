import { supabaseBrowserClient } from "@/supabase/supbaseClient";

export async function signUpClient({
  fullName,
  email,
  password,
  
}: {
  fullName: string;
  email: string;
  password: string;
 
}) {
  const supabase = supabaseBrowserClient;

  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        fullName,
        role: 'Client', 
       
      },
    },
  });

  if (authError) {
    return { error: authError.message };
  }

  const clientAuthId = authData.user?.id;
  if (!clientAuthId) {
    return { error: "Failed to retrieve auth ID for the client." };
  }

  return { clientAuthId, email };
}

export type UpdateClientRelationsParams = {
  clientAuthId: string;
  companyId: string;
  supervisorId: string;
  phone: string;
  gender: string;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  notes: string;
  subscribeToNewsletter: boolean;
};

export async function updateClientRelations({
  clientAuthId,
  companyId,
  supervisorId,
  phone,
  gender,
  billingAddress,
  notes,
  subscribeToNewsletter,
}: UpdateClientRelationsParams) {
  const supabase = supabaseBrowserClient;

  // 1) update the clients row
  const { error: clientError } = await supabase
    .from("clients")
    .update({
      company_id: [companyId],
      supervisor_id: supervisorId,
      phone,
      gender,
      billing_street: billingAddress.street,
      billing_city: billingAddress.city,
      billing_state: billingAddress.state,
      billing_zip: billingAddress.zip,
      billing_country: billingAddress.country,
      notes,
      subscribe_to_newsletter: subscribeToNewsletter,
      updated_by: supervisorId,
    })
    .eq("id", clientAuthId);

  if (clientError) {
    return { error: clientError.message };
  }

  // 2) add to companies.clients array
  const { data: comp, error: fetchError } = await supabase
    .from("companies")
    .select("clients")
    .eq("id", companyId)
    .single();
  if (fetchError) {
    return { error: fetchError.message };
  }

  const existing = comp.clients || [];
  const updated = Array.from(new Set([...existing, clientAuthId]));

  const { error: companyError } = await supabase
    .from("companies")
    .update({ clients: updated })
    .eq("id", companyId);
  if (companyError) {
    return { error: companyError.message };
  }

  return { success: true };
}


  export async function fetchClientsByCompanyId(companyId: string) {
    const supabase = supabaseBrowserClient;
  
    const { data, error } = await supabase
      .from("clients")
      .select("*")
      .contains("company_id", [companyId]); 
  
    if (error) {
      console.error("Error fetching clients:", error.message);
      return { error: error.message };
    }
  
    return { data };
  }

 // /query/clients.ts

export async function fetchClientById(id: string) {
  const supabase = supabaseBrowserClient;

  // 1️⃣ Fetch the client record
  const { data: client, error: clientError } = await supabase
    .from("clients")
    .select(`
      *,
      billing_street,
      billing_city,
      billing_country
    `)
    .eq("id", id)
    .single();

  if (clientError || !client) {
    console.error("Error fetching client:", clientError?.message);
    return { error: clientError?.message || "Client not found" };
  }

  // 2️⃣ Extract the first company_id (if any)
  const companyIds: string[] = client.company_id || [];
  let companyName: string | null = null;

  if (companyIds.length > 0) {
    // 3️⃣ Fetch that company’s name
    const { data: comp, error: compError } = await supabase
      .from("companies")
      .select("name")
      .eq("id", companyIds[0])
      .single();

    if (compError) {
      console.error("Error fetching company:", compError.message);
    } else {
      companyName = comp.name;
    }
  }

  // 4️⃣ Return merged data
  return {
    data: {
      id: client.id,
      full_name: client.full_name,
      email: client.email,
      phone: client.phone,
      company: companyName ?? undefined,
      billingAddress: {
        street: client.billing_street,
        city:   client.billing_city,
        country: client.billing_country,
      },
      walletBalance: client.wallet_balance,
      income: client.income,
      expenses: client.expenses,
      totalDue: client.total_due,
      totalDiscount: client.total_discount,
    },
  };
}

  