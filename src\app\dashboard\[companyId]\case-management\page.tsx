"use client";

// app/dashboard/[companyId]/cases/page.tsx
import { useState, useEffect, Fragment } from "react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { getCasesByCompany, CaseSummary } from "@/query/case_management";

export default function CaseListPage() {
  const { companyId } = useParams() as { companyId: string };
  const [cases, setCases] = useState<CaseSummary[]>([]);
 
  const router = useRouter();
  

  useEffect(() => {
    if (!companyId) return;
    getCasesByCompany(companyId).then(({ data, error }) => {
      if (error) console.error("Error loading cases:", error);
      else if (data) setCases(data);
    });
  }, [companyId]);

  const toggleExpand = (caseId: string) => {
    router.push(`/dashboard/${companyId}/case-management/${caseId}`);
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            Case Management
            <Button asChild>
              <Link href={`/dashboard/${companyId}/add-new-case`}>
                Add New Case
              </Link>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead className="text-right">Budget</TableHead>
                <TableHead className="text-center">Start</TableHead>
                <TableHead className="text-center">End</TableHead>
                <TableHead className="text-center">Status</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cases.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center text-gray-500">
                    No cases available yet.
                  </TableCell>
                </TableRow>
              )}

              {cases.map((c) => (
                <Fragment key={c.id}>
                  <TableRow>
                    <TableCell>{c.case_title}</TableCell>
                    <TableCell className="text-right">
                      {c.case_budget.toLocaleString(undefined, {
                        style: "currency",
                        currency: "ZAR",
                      })}
                    </TableCell>
                    <TableCell className="text-center">{c.start_date}</TableCell>
                    <TableCell className="text-center">{c.end_date}</TableCell>
                    <TableCell className="text-center">{c.status}</TableCell>
                    <TableCell className="text-center">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => toggleExpand(c.id)}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>

                 
                </Fragment>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
