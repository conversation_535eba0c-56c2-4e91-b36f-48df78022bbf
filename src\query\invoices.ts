/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { supabaseBrowserClient } from "@/supabase/supbaseClient";
export type APIResponse<T> = { data?: T; error?: any };

export interface InvoiceData {
  clientId: string;
  companyId: string;
  invoiceItems: {
    id: number;
    item: string;
    description: string;
    quantity: number;
    rate: number;
  }[];
  tax: number;
  discount: number;
  shippingAddress: string;
  notes: string;
  signature: string;
  attachment?: File | null;
  customColumns: {
    rate: string;
    item: string;
    description: string;
    quantity: string;
  };
  paymentSchedule: { dueDate: string; amount: number; note: string }[];
  isDraft: boolean;
  totals: {
    subTotal: number;
    totalTax: number;
    totalAfterDiscount: number;
  };
  bankingDetails: {
    bankName: string;
    branchCode: string;
    accountNumber: string;
  };
}

export async function createInvoice(InvoiceData: InvoiceData) {
  const supabase = supabaseBrowserClient;

  const dataToInsert = {
    client_id: InvoiceData.clientId,
    company_id: InvoiceData.companyId,
    quotation_items: InvoiceData.invoiceItems,
    tax: InvoiceData.tax,
    discount: InvoiceData.discount,
    shipping_address: InvoiceData.shippingAddress,
    notes: InvoiceData.notes,
    signature: InvoiceData.signature,
    attachment: InvoiceData.attachment ? InvoiceData.attachment.name : null,
    custom_columns: InvoiceData.customColumns,
    payment_schedule: InvoiceData.paymentSchedule,
    status: 'pending',
    is_draft: InvoiceData.isDraft,
    is_converted_to_invoice: false, 
    totals: {
      sub_total: InvoiceData.totals.subTotal,
      total_tax: InvoiceData.totals.totalTax,
      total_after_discount: InvoiceData.totals.totalAfterDiscount,
    },
    bank_name: InvoiceData.bankingDetails.bankName,
    branch_code: InvoiceData.bankingDetails.branchCode,
    account_number: InvoiceData.bankingDetails.accountNumber,
  };

  const { data, error } = await supabase
    .from("invoices")
    .insert([dataToInsert]);

  if (error) {
    return { error };
  }
  return { data };
}

export async function getAllInvoices(companyId: string) {
  const supabase = supabaseBrowserClient;
  
  const { data, error } = await supabase
    .from("invoices")
    .select("*, clients(full_name, email), companies(name)")
    .eq("company_id", companyId)

  if (error) {
    return { error };
  }
  
  return { data };
}


export async function getInvoiceById(invoiceId: string) {
  const supabase = supabaseBrowserClient;

  const { data, error } = await supabase
    .from("invoices")
    .select("*, clients(full_name, email), companies(name)")
    .eq("id", invoiceId)
    .single();

  if (error) {
    return { error };
  }
  return { data };
}

export async function updateInvoiceStatus(quotationId: string, newStatus: string): Promise<APIResponse<any>> {
  const supabase = supabaseBrowserClient;
  const isConverted = newStatus === "approved";
  const { data, error } = await supabase
    .from("invoices")
    .update({
      status: newStatus,
      is_converted_to_invoice: isConverted,
      updated_at: new Date().toISOString(),
    })
    .eq("id", quotationId)
    .single();
  return { data, error };
}

export async function convertQuotationToInvoice(quotationId: string): Promise<APIResponse<{ id: string }>> {
  const supabase = supabaseBrowserClient;
  
  const { data: InvoiceData, error: fetchError } = await supabase
    .from("quotations")
    .select("*")
    .eq("id", quotationId)
    .single();
  if (fetchError) return { error: fetchError };
  if (InvoiceData.is_converted_to_invoice) {
    return { error: { message: "Quotation already converted to invoice" } };
  }
  // Update quotation to approved and mark as converted
  const { data: updatedQuotation, error: updateError } = await supabase
    .from("quotations")
    .update({
      status: "approved",
      is_converted_to_invoice: true,
      updated_at: new Date().toISOString(),
    })
    .eq("id", quotationId)
    .single();
  if (updateError) return { error: updateError };

  // Insert into invoices (assuming invoices table structure similar to quotations)
  const invoicePayload = {
    client_id: InvoiceData.client_id,
    company_id: InvoiceData.company_id,
    quotation_items: InvoiceData.quotation_items,
    tax: InvoiceData.tax,
    discount: InvoiceData.discount,
    shipping_address: InvoiceData.shipping_address,
    notes: InvoiceData.notes,
    signature: InvoiceData.signature,
    attachment: InvoiceData.attachment,
    custom_columns: InvoiceData.custom_columns,
    payment_schedule: InvoiceData.payment_schedule,
    status: "approved",
    is_draft: InvoiceData.is_draft,
    is_converted_to_invoice: true,
    totals: InvoiceData.totals,
    reference_number: InvoiceData.reference_number,
    created_at: InvoiceData.created_at,
    updated_at: new Date().toISOString(),
  };

  const { data: invoiceData, error: invoiceError } = await supabase
    .from("invoices")
    .insert([invoicePayload])
    .single();
  if (invoiceError) return { error: invoiceError };

  return { data: invoiceData };
}

export async function updateInvoice(invoiceId: string, updatedData: any) {
  const supabase = supabaseBrowserClient;

  const { data, error } = await supabase
    .from("invoices")
    .update(updatedData)
    .eq("id", invoiceId)
    .single();

  if (error) {
    return { error };
  }
  return { data };
}