/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { getCasesByCase } from "@/query/case_management";

type CaseDetail = {
  id: string;
  case_title: string;
  case_budget: number;
  case_description: string;
  case_expense_board: string;
  start_date: string;
  end_date: string;
  status: string;
  progress: number;
  priority: string;
  customer_name: string;
  customer_can_view: boolean;
  customer_can_comment: boolean;
  assigned_to: string;
  task_communication: string;
  phases: string;
  gantt_charts: string;
  created_at: string;
  updated_at: string;
};

export default function CaseDetailPage() {
  const { companyId, caseId } = useParams() as {
    companyId: string;
    caseId: string;
  };
  const router = useRouter();

  const [caseDetail, setCaseDetail] = useState<CaseDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!caseId) return;
    getCasesByCase(caseId)
      .then(({ data, error }) => {
        if (error) {
          console.error(error);
          setError("Failed to load case.");
        } else if (data?.length) {
          setCaseDetail(data[0]);
        } else {
          setError("Case not found.");
        }
      })
      .finally(() => setLoading(false));
  }, [caseId]);

  if (loading) return <p className="p-6 text-center">Loading…</p>;
  if (error) return <p className="p-6 text-center text-destructive">{error}</p>;
  if (!caseDetail) return null;

  return (
    <div className="min-h-screen p-6 bg-background text-foreground">
      {/* Back Button at the Top Right */}
      <div className="flex justify-between items-center mb-4">
        <div />
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          ← Back to Cases
        </Button>
      </div>

      <Card className="bg-card text-foreground shadow-md">
        <CardHeader>
          <CardTitle className="text-xl">{caseDetail.case_title}</CardTitle>
          <CardDescription className="flex flex-wrap gap-2 pt-2 text-sm">
            <Badge variant="secondary">
              {caseDetail.case_budget.toLocaleString(undefined, {
                style: "currency",
                currency: "ZAR",
              })}
            </Badge>
            <Badge variant="outline">
              {caseDetail.status.replace(/_/g, " ").toUpperCase()}
            </Badge>
            <Badge variant="outline">
              {caseDetail.priority.toUpperCase()}
            </Badge>
            <Badge variant="outline">{caseDetail.progress}%</Badge>
            <span className="ml-auto text-xs text-muted-foreground">
              Created {new Date(caseDetail.created_at).toLocaleDateString()} •{" "}
              Updated {new Date(caseDetail.updated_at).toLocaleDateString()}
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Accordion type="multiple" className="w-full">
            <AccordionItem value="overview">
              <AccordionTrigger>Overview</AccordionTrigger>
              <AccordionContent className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                <div>
                  <Label className="text-muted-foreground">Start Date</Label>
                  <p>{caseDetail.start_date}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">End Date</Label>
                  <p>{caseDetail.end_date}</p>
                </div>
                <div className="md:col-span-2">
                  <Label className="text-muted-foreground">Assigned To</Label>
                  <p>{caseDetail.assigned_to}</p>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="customer">
              <AccordionTrigger>Customer</AccordionTrigger>
              <AccordionContent className="py-4">
                <div>
                  <Label className="text-muted-foreground">Name</Label>
                  <p>{caseDetail.customer_name}</p>
                </div>
                <div className="flex gap-4 mt-2">
                  <Badge variant="outline">
                    View: {caseDetail.customer_can_view ? "Yes" : "No"}
                  </Badge>
                  <Badge variant="outline">
                    Comment: {caseDetail.customer_can_comment ? "Yes" : "No"}
                  </Badge>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="description">
              <AccordionTrigger>Description & Expenses</AccordionTrigger>
              <AccordionContent className="space-y-4 py-4">
                <div>
                  <Label className="text-muted-foreground">Description</Label>
                  <p>{caseDetail.case_description}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Expense Board</Label>
                  <p>{caseDetail.case_expense_board}</p>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="communication">
              <AccordionTrigger>Communication & Phases</AccordionTrigger>
              <AccordionContent className="space-y-4 py-4">
                <div>
                  <Label className="text-muted-foreground">
                    Task Communication
                  </Label>
                  <p>{caseDetail.task_communication}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Phases</Label>
                  <p>{caseDetail.phases}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Gantt Charts</Label>
                  <p>{caseDetail.gantt_charts}</p>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>

      {/* Back Button at the Bottom */}
      <div className="mt-6 flex justify-center">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          ← Back to Cases
        </Button>
      </div>
    </div>
  );
}
