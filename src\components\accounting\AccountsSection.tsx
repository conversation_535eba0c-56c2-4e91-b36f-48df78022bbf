import React, { useState } from 'react';
import { Account } from '@/types/accounting';
import { formatCurrency } from './utils';
import { useToast } from '@/hooks/use-toast';

interface AccountsSectionProps {
  // Only pass essential data and minimal callbacks
}

const AccountsSection: React.FC<AccountsSectionProps> = () => {
  const { toast } = useToast();

  // Move all account-related state here
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);
  const [selectAllAccounts, setSelectAllAccounts] = useState(false);
  const [showAccountDialog, setShowAccountDialog] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [accountSearchTerm, setAccountSearchTerm] = useState('');
  const [accountFilterType, setAccountFilterType] = useState<'all' | 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'>('all');
  const [accountFilterStatus, setAccountFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [accountSortBy, setAccountSortBy] = useState<'code' | 'name' | 'type' | 'balance'>('code');
  const [accountSortOrder, setAccountSortOrder] = useState<'asc' | 'desc'>('asc');

  const [accountForm, setAccountForm] = useState<{
    code: string;
    name: string;
    type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
    category: string;
    parentId: string;
    description: string;
  }>({
    code: '',
    name: '',
    type: 'asset',
    category: '',
    parentId: '',
    description: ''
  });

  // Account CRUD Handlers
  const handleSaveAccount = () => {
    // Form validation
    if (!accountForm.code.trim()) {
      toast({
        title: "Validation Error",
        description: "Account code is required.",
        variant: "destructive",
      });
      return;
    }

    if (!accountForm.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Account name is required.",
        variant: "destructive",
      });
      return;
    }

    // Check for duplicate account code
    const existingAccount = accounts.find(a =>
      a.code === accountForm.code.trim() &&
      (!editingAccount || a.id !== editingAccount.id)
    );

    if (existingAccount) {
      toast({
        title: "Validation Error",
        description: "Account code already exists.",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingAccount) {
        // Update existing account
        const updatedAccounts = accounts.map(account =>
          account.id === editingAccount.id
            ? {
                ...account,
                code: accountForm.code.trim(),
                name: accountForm.name.trim(),
                type: accountForm.type,
                category: accountForm.category.trim(),
                parentId: accountForm.parentId || undefined,
                description: accountForm.description.trim()
              }
            : account
        );
        setAccounts(updatedAccounts);
        toast({
          title: "Success",
          description: "Account updated successfully!",
        });
      } else {
        // Create new account
        const newAccount: Account = {
          id: Date.now().toString(),
          code: accountForm.code.trim(),
          name: accountForm.name.trim(),
          type: accountForm.type,
          category: accountForm.category.trim(),
          parentId: accountForm.parentId || undefined,
          description: accountForm.description.trim(),
          balance: 0,
          isActive: true
        };
        setAccounts([...accounts, newAccount]);
        toast({
          title: "Success",
          description: "Account created successfully!",
        });
      }

      // Reset form and close dialog
      setAccountForm({
        code: '',
        name: '',
        type: 'asset',
        category: '',
        parentId: '',
        description: ''
      });
      setEditingAccount(null);
      setShowAccountDialog(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save account. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditAccount = (account: Account) => {
    setEditingAccount(account);
    setAccountForm({
      code: account.code,
      name: account.name,
      type: account.type,
      category: account.category || '',
      parentId: account.parentId || '',
      description: account.description || ''
    });
    setShowAccountDialog(true);
  };

  const handleDeleteAccount = (accountId: string) => {
    const account = accounts.find(a => a.id === accountId);
    if (!account) return;

    // Check if account has transactions or child accounts
    const hasChildren = accounts.some(a => a.parentId === accountId);
    if (hasChildren) {
      toast({
        title: "Cannot Delete",
        description: "Cannot delete account with child accounts. Please delete child accounts first.",
        variant: "destructive",
      });
      return;
    }

    if (account.balance !== 0) {
      toast({
        title: "Cannot Delete",
        description: "Cannot delete account with non-zero balance.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to delete account "${account.name}"?`)) {
      try {
        const updatedAccounts = accounts.filter(a => a.id !== accountId);
        setAccounts(updatedAccounts);
        toast({
          title: "Success",
          description: "Account deleted successfully!",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete account. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleToggleAccountStatus = (accountId: string) => {
    const updatedAccounts = accounts.map(account =>
      account.id === accountId
        ? { ...account, isActive: !account.isActive }
        : account
    );
    setAccounts(updatedAccounts);

    const account = accounts.find(a => a.id === accountId);
    toast({
      title: "Success",
      description: `Account ${account?.isActive ? 'deactivated' : 'activated'} successfully!`,
    });
  };

  const handleViewAccountLedger = (account: Account) => {
    toast({
      title: "Account Ledger",
      description: `Viewing ledger for ${account.name}`,
    });
    // In a real app, this would open a ledger dialog or navigate to ledger page
  };

  const handleViewAccountHistory = (account: Account) => {
    toast({
      title: "Account History",
      description: `Viewing history for ${account.name}`,
    });
    // In a real app, this would open a history dialog or navigate to history page
  };

  const getFilteredAccounts = () => {
    const filtered = accounts.filter(account => {
      const matchesSearch = account.code.toLowerCase().includes(accountSearchTerm.toLowerCase()) ||
                           account.name.toLowerCase().includes(accountSearchTerm.toLowerCase()) ||
                           account.category?.toLowerCase().includes(accountSearchTerm.toLowerCase());
      const matchesType = accountFilterType === 'all' || account.type === accountFilterType;
      const matchesStatus = accountFilterStatus === 'all' ||
                           (accountFilterStatus === 'active' && account.isActive) ||
                           (accountFilterStatus === 'inactive' && !account.isActive);

      return matchesSearch && matchesType && matchesStatus;
    });

    // Sort accounts
    filtered.sort((a, b) => {
      let aValue, bValue;
      switch (accountSortBy) {
        case 'code':
          aValue = a.code;
          bValue = b.code;
          break;
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'balance':
          aValue = a.balance;
          bValue = b.balance;
          break;
        default:
          aValue = a.code;
          bValue = b.code;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return accountSortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return accountSortOrder === 'asc' ?
        (aValue as number) - (bValue as number) :
        (bValue as number) - (aValue as number);
    });

    return filtered;
  };

  const filteredAccounts = getFilteredAccounts();

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Accounts</h2>
      <table className="min-w-full bg-white border">
        <thead>
          <tr>
            <th>Code</th>
            <th>Name</th>
            <th>Type</th>
            <th>Category</th>
            <th>Balance</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {filteredAccounts.map(account => (
            <tr key={account.id}>
              <td>{account.code}</td>
              <td>{account.name}</td>
              <td>{account.type}</td>
              <td>{account.category}</td>
              <td>{formatCurrency(account.balance)}</td>
              <td>{account.isActive ? 'Active' : 'Inactive'}</td>
              <td>
                <button onClick={() => handleEditAccount(account)}>Edit</button>
                <button onClick={() => handleDeleteAccount(account.id)}>Delete</button>
                <button onClick={() => handleToggleAccountStatus(account.id)}>
                  {account.isActive ? 'Deactivate' : 'Activate'}
                </button>
                <button onClick={() => handleViewAccountLedger(account)}>Ledger</button>
                <button onClick={() => handleViewAccountHistory(account)}>History</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* Add bulk actions, import/export, dialogs, etc. as needed */}
    </div>
  );
};

export default AccountsSection;