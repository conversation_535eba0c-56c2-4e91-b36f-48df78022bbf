/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";
import React, { FC } from 'react';
import Stepper from '@/components/main/stepper';
import Typography from '@/components/ui/typography';
import FirstStepper from '@/components/stepperSignUp/first-stepper';
import SecondStepper from '@/components/stepperSignUp/second-stepper';
import { useSignupValues } from '@/hooks/signup-values';
import { HiSun, HiMoon, HiDesktopComputer } from "react-icons/hi";
import { useTheme } from "next-themes";
import ThirdStepper from '@/components/stepperSignUp/thrid-stepper';
import FourthStepper from '@/components/stepperSignUp/four-stepper';
import FiveStepper from '@/components/stepperSignUp/five-stepper';
import SixthStepper from '@/components/stepperSignUp/sixth-stepper';
import { User } from '@/types/app';

type UserPropss = {
    userData: User;
}
const StepperLayout: FC<UserPropss> = ({userData}) => {
    const { currentStep, totalSteps } = useSignupValues();
    const { setTheme, theme } = useTheme();
    
    const renderStep = () => {
        switch (currentStep) {
            case 1:
                return <FirstStepper />;
            case 2:
                return <SecondStepper />;
            case 3:
                return <ThirdStepper />;
            case 4:
                return <FourthStepper />;
            case 5:
                return <FiveStepper  />;
            case 6:
                return <SixthStepper userId={userData?.id} />;
            default:
                return <div>Unknown Step</div>;
        }
    };

    return (
        <div className="min-h-screen w-full p-4 sm:p-8 grid place-content-center bg-gray-200 dark:bg-gray-900">
            <div className="absolute top-5 right-5">
                <div className="flex space-x-2">
                    <button onClick={() => setTheme('light')} aria-label="Light Mode">
                        <HiSun size={24} className="text-yellow-500" />
                    </button>
                    <button onClick={() => setTheme('dark')} aria-label="Dark Mode">
                        <HiMoon size={24} className="text-gray-500" />
                    </button>
                    <button onClick={() => setTheme('system')} aria-label="System Mode">
                        <HiDesktopComputer size={24} className="text-blue-500" />
                    </button>
                </div>
            </div>
            <div className="w-full mt-10 max-w-3xl border border-neutral-200 p-5 sm:p-8 rounded-lg shadow-lg bg-gray-100 dark:bg-gray-800">
                
                {
                    currentStep !== 6 && (
                        <Stepper currentStep={currentStep} totalSteps={totalSteps} />
                    )
                }
        
                {renderStep()}
            </div>
        </div>
    );
};

export default StepperLayout;
