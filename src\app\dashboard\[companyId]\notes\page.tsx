"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function NotesPage() {
  const [notes, setNotes] = useState<
    { id: string; title: string; content: string }[]
  >([]);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");

  const handleAddNote = () => {
    if (!title.trim() && !content.trim()) return;
    const newNote = {
      id: Date.now().toString(),
      title: title.trim(),
      content: content.trim(),
    };
    setNotes((prev) => [newNote, ...prev]);
    setTitle("");
    setContent("");
  };

  const handleDeleteNote = (id: string) => {
    setNotes((prev) => prev.filter((note) => note.id !== id));
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Add Note Form */}
      <Card>
        <CardHeader>
          <CardTitle>Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                Title
              </label>
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Note Title"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Content
              </label>
              <textarea
                className="w-full border rounded p-2"
                rows={4}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="Write your note here..."
              />
            </div>
            <Button onClick={handleAddNote}>Add Note</Button>
          </div>
        </CardContent>
      </Card>

      {/* Display Notes */}
      {notes.length > 0 && (
        <div className="space-y-4">
          {notes.map((note) => (
            <Card key={note.id}>
              <CardHeader className="flex justify-between items-center">
                <CardTitle>{note.title}</CardTitle>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteNote(note.id)}
                >
                  Delete
                </Button>
              </CardHeader>
              <CardContent>
                <p>{note.content}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
