"use client";

import { <PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import Link from "next/link";

export default function ManageAccountsPage() {
  const accounts = [
    { id: "1", accountNumber: "1001", name: "Cash", type: "Asset", balance: 50000 },
    { id: "2", accountNumber: "1002", name: "Accounts Receivable", type: "Asset", balance: 20000 },
    { id: "3", accountNumber: "2001", name: "Accounts Payable", type: "Liability", balance: 15000 },
    { id: "4", accountNumber: "3001", name: "Office Supplies", type: "Expense", balance: 500 },
    { id: "5", accountNumber: "4001", name: "Consulting Revenue", type: "Income", balance: 10000 },
  ];

  const totalBalance = accounts.reduce((total, account) => total + account.balance, 0);
  const totalAccounts = accounts.length;

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Manage Accounts</CardTitle>
            <div className="flex gap-2">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Account
              </Button>
              <Link href="/dashboard/[companyId]/balancesheet" as="/dashboard/1/balancesheet">
                <Button variant="outline">View Balance Sheet</Button>
              </Link>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Balance and Accounts Total Containers */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Balance</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">${totalBalance}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Accounts</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{totalAccounts}</p>
              </CardContent>
            </Card>
          </div>

          {/* Accounts Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Account Number</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Balance</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {accounts.map((account) => (
                <TableRow key={account.id}>
                  <TableCell>{account.accountNumber}</TableCell>
                  <TableCell>{account.name}</TableCell>
                  <TableCell>${account.balance}</TableCell>
                  <TableCell>{account.type}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline">
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}