"use client"; // Required for client-side interactivity

import { useState } from "react";
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

type TravellingTimeSheet = {
  id: number;
  date: string;
  description: string;
  hours: number;
  rate: number;
};

export default function TravellingTimeSheetPage() {
  const [clientName, setClientName] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [timeSheets, setTimeSheets] = useState<TravellingTimeSheet[]>([]);
  const [newTimeSheet, setNewTimeSheet] = useState<Omit<TravellingTimeSheet, "id">>({
    date: "",
    description: "",
    hours: 0,
    rate: 12,
  });

  const addTimeSheet = () => {
    if (!newTimeSheet.date || !newTimeSheet.description || newTimeSheet.hours <= 0) return;

    const newId = timeSheets.length + 1;
    setTimeSheets([...timeSheets, { ...newTimeSheet, id: newId }]);
    setNewTimeSheet({ date: "", description: "", hours: 0, rate: 12 });
  };

  const calculateTotalAmount = () => {
    return timeSheets.reduce((total, sheet) => total + sheet.hours * sheet.rate, 0);
  };

  const handleCreateInvoice = () => {
    const invoice = {
      clientName,
      companyName,
      timeSheets,
      totalAmount: calculateTotalAmount(),
    };
    console.log("Invoice Created:", invoice);
    alert("Invoice created successfully!");
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Travelling Time Sheet</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Client and Company Details */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Client Name</Label>
                <Input
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Enter client name"
                  required
                />
              </div>
              <div>
                <Label>Company Name</Label>
                <Input
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  placeholder="Enter company name"
                  required
                />
              </div>
            </div>

            {/* Add Travelling Time Sheet */}
            <div className="grid grid-cols-4 gap-4">
              <div>
                <Label>Date</Label>
                <Input
                  type="date"
                  value={newTimeSheet.date}
                  onChange={(e) =>
                    setNewTimeSheet({ ...newTimeSheet, date: e.target.value })
                  }
                  required
                />
              </div>
              <div>
                <Label>Description</Label>
                <Input
                  value={newTimeSheet.description}
                  onChange={(e) =>
                    setNewTimeSheet({ ...newTimeSheet, description: e.target.value })
                  }
                  placeholder="Enter description"
                  required
                />
              </div>
              <div>
                <Label>Hours</Label>
                <Input
                  type="number"
                  value={newTimeSheet.hours}
                  onChange={(e) =>
                    setNewTimeSheet({ ...newTimeSheet, hours: Number(e.target.value) })
                  }
                  required
                />
              </div>
              <div>
                <Label>Rate per Hour (R)</Label>
                <Input
                  type="number"
                  value={newTimeSheet.rate}
                  onChange={(e) =>
                    setNewTimeSheet({ ...newTimeSheet, rate: Number(e.target.value) })
                  }
                  required
                />
              </div>
            </div>
            <Button onClick={addTimeSheet}>
              <Plus className="h-4 w-4 mr-2" />
              Add Time Sheet
            </Button>

            {/* Time Sheets Table */}
            <div>
              <h3 className="font-medium">Travelling Time Sheets</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Hours</TableHead>
                    <TableHead>Rate (R)</TableHead>
                    <TableHead>Amount (R)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {timeSheets.map((sheet) => (
                    <TableRow key={sheet.id}>
                      <TableCell>{sheet.date}</TableCell>
                      <TableCell>{sheet.description}</TableCell>
                      <TableCell>{sheet.hours}</TableCell>
                      <TableCell>{sheet.rate}</TableCell>
                      <TableCell>{(sheet.hours * sheet.rate).toFixed(2)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Total Amount */}
            <div className="flex justify-between">
              <p className="font-medium">Total Amount:</p>
              <p>R{calculateTotalAmount().toFixed(2)}</p>
            </div>

            {/* Create Invoice Button */}
            <div className="flex justify-end">
              <Button onClick={handleCreateInvoice}>Create Invoice</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}