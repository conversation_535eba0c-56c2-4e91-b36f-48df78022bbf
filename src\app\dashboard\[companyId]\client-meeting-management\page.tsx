"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";

interface Meeting {
  id: string;
  client: string;
  title: string;
  date: string;
  time: string;
  notes: string;
}

export default function ManageMeetingsPage() {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [filter, setFilter] = useState("");

  const handleDeleteMeeting = (id: string) => {
    setMeetings((prev) => prev.filter((meeting) => meeting.id !== id));
  };

  const filteredMeetings = meetings.filter((meeting) =>
    meeting.client.toLowerCase().includes(filter.toLowerCase()) ||
    meeting.title.toLowerCase().includes(filter.toLowerCase())
  );

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">All Client Meetings</h1>
        <Input
          type="text"
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          placeholder="Search by client or title..."
          className="w-1/3"
        />
      </div>

      {filteredMeetings.length === 0 ? (
        <p className="text-gray-500">No meetings found.</p>
      ) : (
        <div className="space-y-4">
          {filteredMeetings.map((meeting) => (
            <Card key={meeting.id}>
              <CardHeader className="flex justify-between items-center">
                <div>
                  <CardTitle>{meeting.title} — {meeting.client}</CardTitle>
                  <p className="text-sm text-gray-600">
                    Scheduled on {format(new Date(meeting.date + 'T' + meeting.time), "PPPpp")}
                  </p>
                </div>
                <Button variant="destructive" size="sm" onClick={() => handleDeleteMeeting(meeting.id)}>
                  Delete
                </Button>
              </CardHeader>
              <CardContent>
                {meeting.notes ? <p className="text-gray-700">🗒️ Notes: {meeting.notes}</p> : <p className="text-gray-400 italic">No notes provided.</p>}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
