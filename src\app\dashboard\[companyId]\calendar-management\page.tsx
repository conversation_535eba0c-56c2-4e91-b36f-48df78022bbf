 
"use client";

import { useEffect, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin, { DateClickArg } from "@fullcalendar/interaction";
import type { EventClickArg } from "@fullcalendar/core";
import {
  createCalendarEvent,
  fetchCalendarEvents,
  updateCalendarEvent,
  CalendarEventRecord,
  EventType,
} from "@/query/calendar_event";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Bell, Edit2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useParams } from "next/navigation";
import { toast } from "sonner";

type FormState = {
  company_id:       string;
  belong_to:        string;
  title:            string;
  event_date:       string;
  type:             EventType;
  description?:     string | null;
  reminder_enabled: boolean;
};

export default function CalendarManagerPage() {
  const { companyId } = useParams() as { companyId: string };
  const [currentUser, setCurrentUser]       = useState<string | null>(null);
  const [events, setEvents]                 = useState<CalendarEventRecord[]>([]);
  const [mode, setMode]                     = useState<"add"|"edit"|"view">("add");
  const [selEvent, setSelEvent]             = useState<CalendarEventRecord|null>(null);
  const [isDialogOpen, setIsDialogOpen]     = useState(false);
  const today = new Date().toISOString().slice(0,10);

  const [form, setForm] = useState<FormState>({
    company_id:       companyId,
    belong_to:        "",
    title:            "",
    event_date:       today,
    type:             "invoice",
    description:      null,
    reminder_enabled: true,
  });

  // fetch current user
  useEffect(() => {
    fetch("/api/getUser")
      .then(r => r.json())
      .then(d => {
        if (d.success) {
          const uid = d.user.user.id;
          setCurrentUser(uid);
          setForm(f => ({ ...f, belong_to: uid }));
        }
      })
      .catch(console.error);
  }, []);

  // load calendar events
  useEffect(() => {
    if (!companyId || !currentUser) return;
    fetchCalendarEvents(companyId, currentUser).then(({ data, error }) => {
      if (error) console.error(error);
      else if (data) setEvents(data);
    });
  }, [companyId, currentUser]);

  // map events to FullCalendar
  const fcEvents = events.map(ev => {
    const past = ev.event_date < today;
    const color = past ? "#9CA3AF" : "#3B82F6";
    return {
      id:              ev.id.toString(),
      title:           ev.title,
      date:            ev.event_date,
      backgroundColor: color,
      borderColor:     color,
    };
  });

  // open add dialog (only today/future)
  const openAdd = (dateStr?: string) => {
    if (dateStr && dateStr < today) {
      return toast.error("Cannot add past events");
    }
    setMode("add");
    setSelEvent(null);
    setForm(f => ({
      ...f,
      title:            "",
      description:      null,
      reminder_enabled: true,
      event_date:       dateStr ?? today,
    }));
    setIsDialogOpen(true);
  };
  const onDateClick = (arg: DateClickArg) => openAdd(arg.dateStr);

  // open edit/view dialog
  const onEventClick = (arg: EventClickArg) => {
    const ev = events.find(e => e.id.toString() === arg.event.id);
    if (!ev) return;
    setSelEvent(ev);
    setForm({
      company_id:       ev.company_id,
      belong_to:        ev.belong_to,
      title:            ev.title,
      event_date:       ev.event_date,
      type:             ev.type,
      description:      ev.description ?? null,
      reminder_enabled: ev.reminder_enabled,
    });
    setMode(ev.event_date < today ? "view" : "edit");
    setIsDialogOpen(true);
  };

  // create
  const handleSave = async () => {
    if (!form.title.trim())       return toast.error("Title required");
    if (!form.event_date)         return toast.error("Date required");
    if (form.event_date < today)  return toast.error("Cannot add past");
    if (!currentUser)             return toast.error("Not signed in");

    const { data, error } = await createCalendarEvent({
      ...form,
      created_by: currentUser,
    });
    if (error || !data) toast.error("Create failed");
    else {
      toast.success("Created!");
      setEvents(e => [...e, data]);
      setIsDialogOpen(false);
    }
  };

  // update
  const handleUpdate = async () => {
    if (!selEvent) return; 
    if (!form.title.trim()) return toast.error("Title required");
    if (!currentUser) return toast.error("Not signed in");

    // call Supabase
    const {  error } = await updateCalendarEvent(selEvent.id, {
      title:            form.title,
      event_date:       form.event_date,
      type:             form.type,
      description:      form.description,
      reminder_enabled: form.reminder_enabled,
      updated_by:       currentUser,
    });

    if (error) {
      console.error(error);
      return toast.error("Update failed: " + error.message);
    }

    toast.success("Updated!");

    // re‐load all events so our calendar and local state are in sync
    const { data: fresh, error: fetchErr } = await fetchCalendarEvents(
      companyId,
      currentUser
    );
    if (fetchErr) {
      console.error(fetchErr);
      // but we already showed success, so just log
    } else if (fresh) {
      setEvents(fresh);
    }

    setIsDialogOpen(false);
  };


  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Calendar</CardTitle>
            <div className="flex gap-2">
              <Button onClick={() => openAdd()}>
                <Plus className="mr-2 h-4 w-4"/> Add Event
              </Button>
              <Button variant="outline">
                <Bell className="mr-2 h-4 w-4"/> Reminders
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <FullCalendar
            plugins={[dayGridPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            events={fcEvents}
            dateClick={onDateClick}
            eventClick={onEventClick}
            height="auto"
          />
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {mode === "add"  && "Add Event"}
              {mode === "edit" && "Edit Event"}
              {mode === "view" && "View Event"}
            </DialogTitle>
            {mode !== "view" && (
              <DialogDescription>
                {mode === "add" ? "Fill required fields" : "Modify then save"}
              </DialogDescription>
            )}
          </DialogHeader>

          <div className="space-y-4 mt-2">
            <div>
              <Label>Title *</Label>
              <Input
                value={form.title}
                onChange={e => setForm(f => ({ ...f, title: e.target.value }))}
                disabled={mode==="view"}
              />
            </div>
            <div>
              <Label>Date *</Label>
              <Input
                type="date"
                min={today}
                value={form.event_date}
                onChange={e => setForm(f => ({ ...f, event_date: e.target.value }))}
                disabled={mode==="view"}
              />
            </div>
            <div>
              <Label>Type *</Label>
              <Select
                value={form.type}
                onValueChange={v => setForm(f => ({ ...f, type: v as EventType }))}
                disabled={mode==="view"}
              >
                <SelectTrigger><SelectValue/></SelectTrigger>
                <SelectContent>
                  {["invoice","project","task","meeting","reminder"].map(t=>(
                    <SelectItem key={t} value={t}>
                      {t[0].toUpperCase()+t.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Description</Label>
              <Input
                value={form.description ?? ""}
                onChange={e=>setForm(f=>({...f,description:e.target.value}))}
                disabled={mode==="view"}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={form.reminder_enabled}
                onChange={e=>setForm(f=>({...f,reminder_enabled:e.target.checked}))}
                disabled={mode==="view"}
              />
              <Label>Reminder</Label>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={()=>setIsDialogOpen(false)}>
              Close
            </Button>
            {mode==="add"  && <Button onClick={handleSave}>Save</Button>}
            {mode==="edit" && (
              <Button onClick={handleUpdate}>
                <Edit2 className="mr-2 h-4 w-4"/> Update
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
