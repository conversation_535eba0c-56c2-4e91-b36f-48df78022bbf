/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
"use client";

import React, { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  createTask,
  fetchTasksByCompany,
  fetchMyTasks,
  updateTaskStatus,
  deleteTask as deleteTaskQuery,
} from "@/query/tasks";
import { getEmployeesByCompany } from "@/query/employees";
import { useUserProfile } from "@/query/user";

type RawTaskRow = {
  id: number;
  title: string;
  due_date: string;
  assigned_to: string;
  linked_to_calendar: boolean;
  status: "due" | "progress" | "done";
  notes: string | null;
  assignee: { full_name: string } | null;
};

type Task = {
  id: number;
  title: string;
  dueDate: string;
  assignedToId: string;
  assignedToName: string;
  linkedToCalendar: boolean;
  status: "due" | "progress" | "done";
  notes?: string;
};

type Employee = { id: string; full_name: string };
type User = { id: string; full_name: string };

export default function TaskManagementPage() {
  const { companyId } = useParams() as { companyId: string };

  // 1) current user
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const { profile } = useUserProfile(currentUser?.id, companyId);

  // 2) employees dropdown
  const [employees, setEmployees] = useState<Employee[]>([]);

  // 3) tasks
  const [tasks, setTasks] = useState<Task[]>([]);

  // 4) form state
  const [newTaskTitle, setNewTaskTitle] = useState("");
  const [newTaskDueDate, setNewTaskDueDate] = useState("");
  const [newTaskAssignedTo, setNewTaskAssignedTo] = useState("");
  const [newTaskNotes, setNewTaskNotes] = useState("");
  const [linkedToCalendar, setLinkedToCalendar] = useState(true);
  const [newTaskStatus, setNewTaskStatus] = useState<"due" | "progress" | "done">("due");

  // Load current user
  useEffect(() => {
    fetch("/api/getUser")
      .then((r) => r.json())
      .then((d) => {
        if (d.success) {
          setCurrentUser(d.user.user);
          setNewTaskAssignedTo(d.user.user.id);
        }
      })
      .catch(console.error);
  }, []);

  // Load employees
  useEffect(() => {
    if (!companyId) return;
    getEmployeesByCompany(companyId).then(({ data, error }) => {
      if (error) console.error(error);
      else setEmployees(data);
    });
  }, [companyId]);

  // Load tasks (with assignee join)
  useEffect(() => {
    if (!companyId || !currentUser) return;
    fetchMyTasks(companyId, currentUser.id).then(({ data, error }) => {
      if (error) return console.error(error);
      const ui = (data as RawTaskRow[]).map((r) => ({
        id: r.id,
        title: r.title,
        dueDate: r.due_date,
        assignedToId: r.assigned_to,
        assignedToName: r.assignee?.full_name ?? "(unknown)",
        linkedToCalendar: r.linked_to_calendar,
        status: r.status,
        notes: r.notes ?? undefined,
      }));
      setTasks(ui);
    });
  }, [companyId, currentUser]);

  // Create
  const addTask = async () => {
    if (!newTaskTitle.trim()) return toast.error("Title is required");
    if (!newTaskDueDate) return toast.error("Due date is required");
    if (!newTaskAssignedTo) return toast.error("Please assign the task");

    const payload = {
      companyId,
      title: newTaskTitle,
      dueDate: newTaskDueDate,
      assignedTo: newTaskAssignedTo,
      linkedToCalendar,
      status: newTaskStatus,
      notes: newTaskNotes,
      createdBy: currentUser!.id,
    };
    const { data, error } = await createTask(payload);
    if (error) {
      console.error(error);
      return toast.error("Failed to create task");
    }
    setTasks((t) => [
      ...t,
      {
        id: data.id,
        title: data.title,
        dueDate: data.due_date,
        assignedToId: data.assigned_to,
        assignedToName: profile?.full_name ?? data.assigned_to,
        linkedToCalendar: data.linked_to_calendar,
        status: data.status,
        notes: data.notes ?? undefined,
      },
    ]);
    toast.success("Task added");

    // reset
    setNewTaskTitle("");
    setNewTaskDueDate("");
    setNewTaskAssignedTo(currentUser!.id);
    setNewTaskNotes("");
    setLinkedToCalendar(true);
    setNewTaskStatus("due");
  };

  // Move status
  const moveTask = async (id: number, status: Task["status"]) => {
    const { error } = await updateTaskStatus(id, status, currentUser!.id);
    if (error) {
      console.error(error);
      return toast.error("Failed to update status");
    }
    setTasks((t) => t.map((x) => (x.id === id ? { ...x, status } : x)));
    toast.success(`Marked ${status}`);
  };

  // Delete
  const deleteTask = async (id: number) => {
    const { error } = await deleteTaskQuery(id);
    if (error) {
      console.error(error);
      return toast.error("Delete failed");
    }
    setTasks((t) => t.filter((x) => x.id !== id));
    toast.success("Deleted");
  };

  const byStatus = (s: Task["status"]) => tasks.filter((t) => t.status === s);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Task Management</h1>

      {/* Add New Task */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Add New Task</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Input
              value={newTaskTitle}
              onChange={(e) => setNewTaskTitle(e.target.value)}
              placeholder="Task Title"
            />
            <Input
              type="date"
              value={newTaskDueDate}
              onChange={(e) => setNewTaskDueDate(e.target.value)}
            />
            <div>
              <Label>Assign To</Label>
              <Select
                value={newTaskAssignedTo}
                onValueChange={setNewTaskAssignedTo}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={currentUser?.id!}>
                    {profile?.full_name} (You)
                  </SelectItem>
                  {employees.map((e) => (
                    <SelectItem key={e.id} value={e.id}>
                      {e.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Textarea
              placeholder="Notes"
              value={newTaskNotes}
              onChange={(e) => setNewTaskNotes(e.target.value)}
            />
            <div className="flex items-center space-x-4">
              <Label>Link Calendar</Label>
              <Switch
                checked={linkedToCalendar}
                onCheckedChange={setLinkedToCalendar}
              />
            </div>
            <Select
              value={newTaskStatus}
              onValueChange={(v) => setNewTaskStatus(v as Task["status"])}
            >
              <SelectTrigger>
                <SelectValue placeholder="Status..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="due">Due</SelectItem>
                <SelectItem value="progress">In Progress</SelectItem>
                <SelectItem value="done">Done</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={addTask}>Add Task</Button>
          </div>
        </CardContent>
      </Card>

      {/* Task Boards */}
      <div className="grid md:grid-cols-3 gap-6">
        {(["due", "progress", "done"] as Task["status"][]).map((s) => (
          <Card key={s}>
            <CardHeader>
              <CardTitle
                className={
                  s === "due"
                    ? "text-red-500"
                    : s === "progress"
                    ? "text-yellow-500"
                    : "text-green-500"
                }
              >
                {s === "due"
                  ? "Due"
                  : s === "progress"
                  ? "In Progress"
                  : "Done"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {byStatus(s).map((task) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onMoveTask={moveTask}
                  onDeleteTask={deleteTask}
                />
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Total Tasks: {tasks.length}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">
            You can link tasks to the calendar and receive reminders.
          </p>
        </CardContent>
      </Card>

      {/* Notification Board */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Notification Board</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside text-sm text-gray-700">
            <li>Task deadlines approaching...</li>
            <li>Tasks moved to done today...</li>
            <li>Reminder: link to calendar for auto‑reminders.</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}

type TaskCardProps = {
  task: Task;
  onMoveTask: (id: number, status: Task["status"]) => void;
  onDeleteTask: (id: number) => void;
};
function TaskCard({
  task,
  onMoveTask,
  onDeleteTask,
}: TaskCardProps) {
  // show only the other two actions
  const other = (["due", "progress", "done"] as Task["status"][]).filter(
    (x) => x !== task.status
  );

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <h3 className="font-bold">{task.title}</h3>
        <p className="text-sm text-gray-500">Due: {task.dueDate}</p>
        <p className="text-xs text-gray-400">Assigned: {task.assignedToName}</p>
        {task.notes && (
          <p className="text-xs text-gray-600 mt-1">Notes: {task.notes}</p>
        )}
        <div className="flex gap-2 mt-2">
          {other.map((s) => (
            <Button
              key={s}
              size="sm"
              variant="outline"
              onClick={() => onMoveTask(task.id, s)}
            >
              {s === "due"
                ? "Due"
                : s === "progress"
                ? "In Progress"
                : "Done"}
            </Button>
          ))}
          <Button
            size="sm"
            variant="destructive"
            onClick={() => onDeleteTask(task.id)}
          >
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
