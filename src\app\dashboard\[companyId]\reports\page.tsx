"use client";

import React, { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Download, FileX, FileText } from "lucide-react";

export default function ReportsPage() {
  const [reportType, setReportType] = useState("Invoices");

  // Dummy report data (replace with actual report data)
  const reportData = [
    { id: 1, name: "Report Item 1", detail: "Detail 1" },
    { id: 2, name: "Report Item 2", detail: "Detail 2" },
    { id: 3, name: "Report Item 3", detail: "Detail 3" },
  ];

  const handleExport = (format: string) => {
    // Implement your export logic here
    alert(`Exporting ${reportType} report as ${format}`);
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:justify-between md:items-center">
          <CardTitle>Reports</CardTitle>
          <div className="flex items-center gap-4 mt-4 md:mt-0">
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Report" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Invoices">Invoices</SelectItem>
                <SelectItem value="Estimates">Estimates/Quotes</SelectItem>
                <SelectItem value="Employees">Employees</SelectItem>
                <SelectItem value="Clients">Clients</SelectItem>
                <SelectItem value="Accounting">Accounting</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => handleExport("Excel")}>
              <Download className="h-4 w-4 mr-2" />
              Excel
            </Button>
            <Button onClick={() => handleExport("CSV")} variant="outline">
              <FileX className="h-4 w-4 mr-2" />
              CSV
            </Button>
            <Button onClick={() => handleExport("PDF")} variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              PDF
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Dummy Report Data Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Detail</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.id}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.detail}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
