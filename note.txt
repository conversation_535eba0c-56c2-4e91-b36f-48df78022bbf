project name: sebenza-app
Database Password: Sebenza-app123

//command to get the types from supabase

npx supabase gen types typescript --project-id "rrygblxrsvlwowgzwzgk" --schema public > src/types/supabase.ts

//Deployment link push there
git remote set-url origin https://github.com/johnmuninga/sebenza-app-data.git

//Sebenza Organisation
git remote set-url origin https://github.com/SebemzaMoses/sebenza-app.git

// sql code

-- Create 'users' table with supervisor_id
CREATE TABLE users (
    id UUID NOT NULL PRIMARY KEY, -- Link to auth.users
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    email VARCHAR(255),
    role VARCHAR(50) CHECK (role IN ('CEO', 'Employee', 'Client')) NOT NULL,
    company_id TEXT, -- Company ID is a text field
    supervisor_id TEXT, -- Added supervisor_id as a text column
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Drop Row Level Security on the 'users' table (if it exists)
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Create 'companies' table
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    employees UUID[], -- Array of user IDs of employees
    clients UUID[], -- Array of user IDs of clients
    ceo_id UUID REFERENCES users(id), -- ID of the CEO
    years_in_business VARCHAR(255),
    currency VARCHAR(50),
    subscription_status VARCHAR(50) CHECK (subscription_status IN ('Active', 'Inactive', 'Frozen')) DEFAULT 'Active',
    subscription_type VARCHAR(255), -- Type of subscription
    number_of_users INT,
    industry VARCHAR(255),
    country VARCHAR(100),
    city VARCHAR(255),
    language VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create 'employees' table
CREATE TABLE employees (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    email VARCHAR(255),
    avatar_url TEXT,
    position VARCHAR(255),
    salary DECIMAL(15, 2),
    hired_at DATE,
    supervisor_id TEXT, -- Changed to text for consistency
    company_id TEXT, -- Company ID is a text field
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Drop Row Level Security on the 'employees' table (if it exists)
ALTER TABLE employees DISABLE ROW LEVEL SECURITY;

-- Create 'clients' table
CREATE TABLE clients (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    email VARCHAR(255),
    avatar_url TEXT,
    package VARCHAR(255),
    subscription_status VARCHAR(50) CHECK (subscription_status IN ('Active', 'Inactive', 'Frozen')) DEFAULT 'Active',
    subscription_start DATE,
    subscription_end DATE,
    company_id TEXT, -- Company ID is a text field
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Function to handle new user creation and set role
CREATE OR REPLACE FUNCTION public.handle_new_user() RETURNS TRIGGER AS $$
BEGIN
    -- Temporarily disable RLS for the duration of the trigger
    EXECUTE 'SET LOCAL row_security = off';

    -- Insert or update the record in the 'users' table
    INSERT INTO public.users (id, full_name, email, role, supervisor_id, created_at)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data ->> 'fullName', -- Extract full name from metadata
        NEW.email,
        CASE
            WHEN NEW.raw_user_meta_data ->> 'role' IS NOT NULL THEN NEW.raw_user_meta_data ->> 'role'
            ELSE 'Employee' -- Default to Employee if role is not provided
        END,
        NEW.raw_user_meta_data ->> 'supervisorId', -- Extract supervisor_id from metadata
        NOW()
    )
    ON CONFLICT (id) DO UPDATE
    SET full_name = EXCLUDED.full_name,
        email = EXCLUDED.email,
        role = EXCLUDED.role,
        supervisor_id = EXCLUDED.supervisor_id,
        updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new employee creation
CREATE OR REPLACE FUNCTION public.handle_new_employee() RETURNS TRIGGER AS $$
BEGIN
    -- Temporarily disable RLS for the duration of the trigger
    EXECUTE 'SET LOCAL row_security = off';

    -- Insert into the 'employees' table
    INSERT INTO public.employees (id, full_name, email, avatar_url, position, salary, hired_at, supervisor_id, company_id, created_at)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data ->> 'fullName',
        NEW.email,
        NEW.raw_user_meta_data ->> 'avatarUrl',
        NEW.raw_user_meta_data ->> 'position',
        CAST(NEW.raw_user_meta_data ->> 'salary' AS DECIMAL),
        TO_DATE(NEW.raw_user_meta_data ->> 'hiredAt', 'YYYY-MM-DD'),
        NEW.raw_user_meta_data ->> 'supervisorId',
        NEW.raw_user_meta_data ->> 'companyId',
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new client creation
CREATE OR REPLACE FUNCTION public.handle_new_client() RETURNS TRIGGER AS $$
BEGIN
    -- Temporarily disable RLS for the duration of the trigger
    EXECUTE 'SET LOCAL row_security = off';

    -- Insert into the 'clients' table
    INSERT INTO public.clients (id, full_name, email, avatar_url, package, subscription_status, subscription_start, subscription_end, company_id, created_at)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data ->> 'fullName',
        NEW.email,
        NEW.raw_user_meta_data ->> 'avatarUrl',
        NEW.raw_user_meta_data ->> 'package',
        COALESCE(NEW.raw_user_meta_data ->> 'subscriptionStatus', 'Active'),
        TO_DATE(NEW.raw_user_meta_data ->> 'subscriptionStart', 'YYYY-MM-DD'),
        TO_DATE(NEW.raw_user_meta_data ->> 'subscriptionEnd', 'YYYY-MM-DD'),
        NEW.raw_user_meta_data ->> 'companyId',
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new users in auth.users
CREATE OR REPLACE TRIGGER on_new_user
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- Trigger for new employees in auth.users
CREATE OR REPLACE TRIGGER on_new_employee
AFTER INSERT ON auth.users
FOR EACH ROW
WHEN (NEW.raw_user_meta_data ->> 'role' = 'Employee')
EXECUTE FUNCTION public.handle_new_employee();

-- Trigger for new clients in auth.users
CREATE OR REPLACE TRIGGER on_new_client
AFTER INSERT ON auth.users
FOR EACH ROW
WHEN (NEW.raw_user_meta_data ->> 'role' = 'Client')
EXECUTE FUNCTION public.handle_new_client();

