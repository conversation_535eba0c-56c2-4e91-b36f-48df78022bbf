"use client"; // Required for client-side interactivity

import { useState} from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Play, Pause, StopCircle, Plus } from "lucide-react";

import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

type Timer = {
  id: number;
  startTime: number;
  endTime: number | null;
  description: string;
};

export default function TimeTrackingPage() {
  const [timers, setTimers] = useState<Timer[]>([]);
  const [activeTimer, setActiveTimer] = useState<number | null>(null);
  const [ratePerHour, setRatePerHour] = useState(12);
  const [taxRate, setTaxRate] = useState(10);
  const [manualTime, setManualTime] = useState("");

  const startTimer = () => {
    const newTimer: Timer = {
      id: Date.now(),
      startTime: Date.now(),
      endTime: null,
      description: "",
    };
    setTimers([...timers, newTimer]);
    setActiveTimer(newTimer.id);
  };

  const pauseTimer = () => {
    setActiveTimer(null);
  };

  const stopTimer = (id: number) => {
    setTimers((prev) =>
      prev.map((timer) =>
        timer.id === id ? { ...timer, endTime: Date.now() } : timer
      )
    );
    setActiveTimer(null);
  };

  const addManualTime = () => {
    if (!manualTime) return;

    const [hours, minutes, seconds] = manualTime.split(":").map(Number);
    const totalSeconds = hours * 3600 + minutes * 60 + seconds;
    const newTimer: Timer = {
      id: Date.now(),
      startTime: Date.now() - totalSeconds * 1000,
      endTime: Date.now(),
      description: "Manual Entry",
    };
    setTimers([...timers, newTimer]);
    setManualTime("");
  };

  const calculateTotalAmount = () => {
    const totalSeconds = timers.reduce((total, timer) => {
      const endTime = timer.endTime || Date.now();
      return total + (endTime - timer.startTime) / 1000;
    }, 0);
    const totalHours = totalSeconds / 3600;
    return totalHours * ratePerHour;
  };

  const calculateTotalWithTax = () => {
    const totalAmount = calculateTotalAmount();
    return totalAmount * (1 + taxRate / 100);
  };

  const handleCreateInvoice = () => {
    const invoice = {
      timers,
      totalAmount: calculateTotalAmount(),
      totalWithTax: calculateTotalWithTax(),
    };
    console.log("Invoice Created:", invoice);
    alert("Invoice created successfully!");
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Time Tracking</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Timer Controls */}
            <div className="flex gap-2">
              <Button onClick={startTimer} disabled={activeTimer !== null}>
                <Play className="h-4 w-4 mr-2" />
                Start
              </Button>
              <Button onClick={pauseTimer} disabled={activeTimer === null}>
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
              <Button
                onClick={() => activeTimer && stopTimer(activeTimer)}
                disabled={activeTimer === null}
              >
                <StopCircle className="h-4 w-4 mr-2" />
                Stop
              </Button>
            </div>

            {/* Manual Time Entry */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Enter Time Manually (HH:MM:SS)</Label>
                <Input
                  value={manualTime}
                  onChange={(e) => setManualTime(e.target.value)}
                  placeholder="01:30:14"
                />
              </div>
              <div className="flex items-end">
                <Button onClick={addManualTime}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Manual Time
                </Button>
              </div>
            </div>

            {/* Rate and Tax */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Rate per Hour (R)</Label>
                <Input
                  type="number"
                  value={ratePerHour}
                  onChange={(e) => setRatePerHour(Number(e.target.value))}
                />
              </div>
              <div>
                <Label>VAT/Tax (%)</Label>
                <Input
                  type="number"
                  value={taxRate}
                  onChange={(e) => setTaxRate(Number(e.target.value))}
                />
              </div>
            </div>

            {/* Timers Table */}
            <div>
              <h3 className="font-medium">Timers</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {timers.map((timer) => {
                    const endTime = timer.endTime || Date.now();
                    const duration = (endTime - timer.startTime) / 1000;
                    const hours = Math.floor(duration / 3600);
                    const minutes = Math.floor((duration % 3600) / 60);
                    const seconds = Math.floor(duration % 60);
                    const amount = (duration / 3600) * ratePerHour;

                    return (
                      <TableRow key={timer.id}>
                        <TableCell>{timer.description}</TableCell>
                        <TableCell>{`${hours}:${minutes}:${seconds}`}</TableCell>
                        <TableCell>R{amount.toFixed(2)}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>

            {/* Total Amount */}
            <div className="flex justify-between">
              <p className="font-medium">Total Amount:</p>
              <p>R{calculateTotalAmount().toFixed(2)}</p>
            </div>
            <div className="flex justify-between">
              <p className="font-medium">Total with VAT/Tax:</p>
              <p>R{calculateTotalWithTax().toFixed(2)}</p>
            </div>

            {/* Create Invoice Button */}
            <div className="flex justify-end">
              <Button onClick={handleCreateInvoice}>Create Invoice</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}