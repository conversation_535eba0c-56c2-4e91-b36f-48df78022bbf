'use client'
import React from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import Typography from '@/components/ui/typography';
import { FiMail } from 'react-icons/fi';
import { useTheme } from 'next-themes';
import { HiSun, HiMoon, HiDesktopComputer } from 'react-icons/hi';
function Page() {
  const router = useRouter();
  const { setTheme } = useTheme();

  const handleResendEmail = () => {
   
    alert('Verification email has been resent.');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-200 dark:bg-gray-900">
      <div className="absolute top-5 right-5">
        <div className="flex space-x-2">
            <button onClick={() => setTheme('light')} aria-label="Light Mode">
                <HiSun size={24} className="text-yellow-500" />
            </button>
            <button onClick={() => setTheme('dark')} aria-label="Dark Mode">
                <HiMoon size={24} className="text-gray-500" />
            </button>
            <button onClick={() => setTheme('system')} aria-label="System Mode">
                <HiDesktopComputer size={24} className="text-blue-500" />
            </button>
        </div>
      </div>
      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-md text-center">
        <div className="flex justify-center mb-6">
          <FiMail size={48} className="text-blue-600 dark:text-blue-400" />
        </div>
        <Typography
          text="Check your Email"
          variant="h2"
          className="text-2xl font-bold mb-4 text-gray-800 dark:text-white"
        />
        <Typography
          text="We’ve sent you an email with a link to verify your account."
          variant="p"
          className="mb-6 text-gray-600 dark:text-gray-400"
        />
        <Button
          onClick={handleResendEmail}
          className="bg-blue-600 dark:text-white hover:bg-blue-500 dark:bg-blue-700 dark:hover:bg-blue-600 text-white px-6 py-3 rounded-lg mb-4 mr-5"
        >
          Resend Email
        </Button>
        <Button
          onClick={() => router.push('/auth/login')}
          className="bg-gray-400 dark:text-white hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-white px-6 py-3 rounded-lg"
        >
          Back to Login
        </Button>
      </div>
    </div>
  );
}

export default Page;
