export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      calendar_event: {
        Row: {
          belong_to: string
          company_id: string
          created_at: string
          created_by: string
          deleted_at: string | null
          deleted_by: string | null
          description: string | null
          event_date: string
          id: number
          reminder_enabled: boolean
          title: string
          type: Database["public"]["Enums"]["event_type"]
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          belong_to: string
          company_id: string
          created_at?: string
          created_by: string
          deleted_at?: string | null
          deleted_by?: string | null
          description?: string | null
          event_date: string
          id?: number
          reminder_enabled?: boolean
          title: string
          type: Database["public"]["Enums"]["event_type"]
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          belong_to?: string
          company_id?: string
          created_at?: string
          created_by?: string
          deleted_at?: string | null
          deleted_by?: string | null
          description?: string | null
          event_date?: string
          id?: number
          reminder_enabled?: boolean
          title?: string
          type?: Database["public"]["Enums"]["event_type"]
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "calendar_event_belong_to_fkey"
            columns: ["belong_to"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "calendar_event_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "calendar_event_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "calendar_event_deleted_by_fkey"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "calendar_event_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      case_management: {
        Row: {
          assigned_to: string | null
          case_budget: number | null
          case_description: string | null
          case_expense_board: string | null
          case_title: string
          company_id: string
          created_at: string
          created_by: string | null
          customer_can_comment: boolean | null
          customer_can_view: boolean | null
          customer_name: string | null
          end_date: string | null
          gantt_charts: string | null
          id: string
          phases: string | null
          priority: string | null
          progress: number | null
          start_date: string | null
          status: string | null
          task_communication: string | null
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          assigned_to?: string | null
          case_budget?: number | null
          case_description?: string | null
          case_expense_board?: string | null
          case_title: string
          company_id: string
          created_at?: string
          created_by?: string | null
          customer_can_comment?: boolean | null
          customer_can_view?: boolean | null
          customer_name?: string | null
          end_date?: string | null
          gantt_charts?: string | null
          id?: string
          phases?: string | null
          priority?: string | null
          progress?: number | null
          start_date?: string | null
          status?: string | null
          task_communication?: string | null
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          assigned_to?: string | null
          case_budget?: number | null
          case_description?: string | null
          case_expense_board?: string | null
          case_title?: string
          company_id?: string
          created_at?: string
          created_by?: string | null
          customer_can_comment?: boolean | null
          customer_can_view?: boolean | null
          customer_name?: string | null
          end_date?: string | null
          gantt_charts?: string | null
          id?: string
          phases?: string | null
          priority?: string | null
          progress?: number | null
          start_date?: string | null
          status?: string | null
          task_communication?: string | null
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "case_management_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_management_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_management_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_management_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          avatar_url: string | null
          billing_city: string | null
          billing_country: string | null
          billing_state: string | null
          billing_street: string | null
          billing_zip: string | null
          company_id: string[] | null
          created_at: string | null
          created_by: string | null
          email: string | null
          full_name: string | null
          gender: string | null
          id: string
          notes: string | null
          package: string | null
          phone: string | null
          subscribe_to_newsletter: boolean
          subscription_end: string | null
          subscription_start: string | null
          subscription_status: string | null
          supervisor_id: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          avatar_url?: string | null
          billing_city?: string | null
          billing_country?: string | null
          billing_state?: string | null
          billing_street?: string | null
          billing_zip?: string | null
          company_id?: string[] | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          full_name?: string | null
          gender?: string | null
          id: string
          notes?: string | null
          package?: string | null
          phone?: string | null
          subscribe_to_newsletter?: boolean
          subscription_end?: string | null
          subscription_start?: string | null
          subscription_status?: string | null
          supervisor_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          avatar_url?: string | null
          billing_city?: string | null
          billing_country?: string | null
          billing_state?: string | null
          billing_street?: string | null
          billing_zip?: string | null
          company_id?: string[] | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          full_name?: string | null
          gender?: string | null
          id?: string
          notes?: string | null
          package?: string | null
          phone?: string | null
          subscribe_to_newsletter?: boolean
          subscription_end?: string | null
          subscription_start?: string | null
          subscription_status?: string | null
          supervisor_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clients_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clients_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clients_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      companies: {
        Row: {
          address: string | null
          ceo_id: string | null
          city: string | null
          clients: string[] | null
          country: string | null
          created_at: string | null
          currency: string | null
          employees: string[] | null
          id: string
          industry: string | null
          language: string | null
          name: string
          number_of_users: number | null
          phone: string | null
          subscription_status: string | null
          subscription_type: string | null
          updated_at: string | null
          years_in_business: string | null
        }
        Insert: {
          address?: string | null
          ceo_id?: string | null
          city?: string | null
          clients?: string[] | null
          country?: string | null
          created_at?: string | null
          currency?: string | null
          employees?: string[] | null
          id?: string
          industry?: string | null
          language?: string | null
          name: string
          number_of_users?: number | null
          phone?: string | null
          subscription_status?: string | null
          subscription_type?: string | null
          updated_at?: string | null
          years_in_business?: string | null
        }
        Update: {
          address?: string | null
          ceo_id?: string | null
          city?: string | null
          clients?: string[] | null
          country?: string | null
          created_at?: string | null
          currency?: string | null
          employees?: string[] | null
          id?: string
          industry?: string | null
          language?: string | null
          name?: string
          number_of_users?: number | null
          phone?: string | null
          subscription_status?: string | null
          subscription_type?: string | null
          updated_at?: string | null
          years_in_business?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "companies_ceo_id_fkey"
            columns: ["ceo_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      employees: {
        Row: {
          avatar_url: string | null
          company_id: string[] | null
          created_at: string | null
          email: string | null
          full_name: string | null
          hired_at: string | null
          id: string
          position: string | null
          salary: number | null
          supervisor_id: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          company_id?: string[] | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          hired_at?: string | null
          id: string
          position?: string | null
          salary?: number | null
          supervisor_id?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          company_id?: string[] | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          hired_at?: string | null
          id?: string
          position?: string | null
          salary?: number | null
          supervisor_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "employees_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          account_number: string | null
          attachment: string | null
          bank_name: string | null
          branch_code: string | null
          client_id: string
          company_id: string
          created_at: string
          custom_columns: Json
          discount: number
          due_date: string | null
          id: string
          invoice_date: string | null
          is_converted_to_invoice: boolean
          is_draft: boolean
          notes: string | null
          payment_schedule: Json | null
          quotation_items: Json
          reference_number: string | null
          shipping_address: string | null
          signature: string | null
          status: string
          tax: number
          totals: Json
          updated_at: string
        }
        Insert: {
          account_number?: string | null
          attachment?: string | null
          bank_name?: string | null
          branch_code?: string | null
          client_id: string
          company_id: string
          created_at?: string
          custom_columns: Json
          discount?: number
          due_date?: string | null
          id?: string
          invoice_date?: string | null
          is_converted_to_invoice?: boolean
          is_draft?: boolean
          notes?: string | null
          payment_schedule?: Json | null
          quotation_items: Json
          reference_number?: string | null
          shipping_address?: string | null
          signature?: string | null
          status?: string
          tax?: number
          totals: Json
          updated_at?: string
        }
        Update: {
          account_number?: string | null
          attachment?: string | null
          bank_name?: string | null
          branch_code?: string | null
          client_id?: string
          company_id?: string
          created_at?: string
          custom_columns?: Json
          discount?: number
          due_date?: string | null
          id?: string
          invoice_date?: string | null
          is_converted_to_invoice?: boolean
          is_draft?: boolean
          notes?: string | null
          payment_schedule?: Json | null
          quotation_items?: Json
          reference_number?: string | null
          shipping_address?: string | null
          signature?: string | null
          status?: string
          tax?: number
          totals?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_client_invoice"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_company_invoice"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      quotations: {
        Row: {
          account_number: string | null
          attachment: string | null
          bank_name: string | null
          branch_code: string | null
          client_id: string
          company_id: string
          created_at: string
          custom_columns: Json
          discount: number
          expiry_date: string | null
          id: string
          is_converted_to_invoice: boolean
          is_draft: boolean
          notes: string | null
          payment_schedule: Json | null
          quotation_date: string | null
          quotation_items: Json
          reference_number: string | null
          shipping_address: string | null
          signature: string | null
          status: string
          tax: number
          totals: Json
          updated_at: string
        }
        Insert: {
          account_number?: string | null
          attachment?: string | null
          bank_name?: string | null
          branch_code?: string | null
          client_id: string
          company_id: string
          created_at?: string
          custom_columns: Json
          discount?: number
          expiry_date?: string | null
          id?: string
          is_converted_to_invoice?: boolean
          is_draft?: boolean
          notes?: string | null
          payment_schedule?: Json | null
          quotation_date?: string | null
          quotation_items: Json
          reference_number?: string | null
          shipping_address?: string | null
          signature?: string | null
          status?: string
          tax?: number
          totals: Json
          updated_at?: string
        }
        Update: {
          account_number?: string | null
          attachment?: string | null
          bank_name?: string | null
          branch_code?: string | null
          client_id?: string
          company_id?: string
          created_at?: string
          custom_columns?: Json
          discount?: number
          expiry_date?: string | null
          id?: string
          is_converted_to_invoice?: boolean
          is_draft?: boolean
          notes?: string | null
          payment_schedule?: Json | null
          quotation_date?: string | null
          quotation_items?: Json
          reference_number?: string | null
          shipping_address?: string | null
          signature?: string | null
          status?: string
          tax?: number
          totals?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_client"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_company"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assigned_to: string
          company_id: string
          created_at: string
          created_by: string
          deleted_at: string | null
          deleted_by: string | null
          due_date: string
          id: number
          linked_to_calendar: boolean
          notes: string | null
          status: Database["public"]["Enums"]["task_status"]
          title: string
          updated_at: string
          updated_by: string
        }
        Insert: {
          assigned_to: string
          company_id: string
          created_at?: string
          created_by: string
          deleted_at?: string | null
          deleted_by?: string | null
          due_date: string
          id?: number
          linked_to_calendar?: boolean
          notes?: string | null
          status?: Database["public"]["Enums"]["task_status"]
          title: string
          updated_at?: string
          updated_by: string
        }
        Update: {
          assigned_to?: string
          company_id?: string
          created_at?: string
          created_by?: string
          deleted_at?: string | null
          deleted_by?: string | null
          due_date?: string
          id?: number
          linked_to_calendar?: boolean
          notes?: string | null
          status?: Database["public"]["Enums"]["task_status"]
          title?: string
          updated_at?: string
          updated_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_deleted_by_fkey"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          company_id: string[] | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          role: string
          supervisor_id: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          company_id?: string[] | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          role: string
          supervisor_id?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          company_id?: string[] | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          role?: string
          supervisor_id?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_employee_to_company: {
        Args:
          | { employee_id: string; company_id: string }
          | { employee_id: string; company_id: string }
        Returns: undefined
      }
      convert_quotation_to_invoice: {
        Args: { p_quotation_id: string; p_new_status: string }
        Returns: string
      }
      set_config: {
        Args: { key: string; value: string }
        Returns: undefined
      }
      set_user_context: {
        Args: { user_id: string } | { user_id: string; role: string }
        Returns: undefined
      }
      update_employee_permissions: {
        Args: { employee_id: string; new_permissions: string[] }
        Returns: undefined
      }
    }
    Enums: {
      event_type: "invoice" | "project" | "task" | "meeting" | "reminder"
      task_status: "due" | "progress" | "done"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      event_type: ["invoice", "project", "task", "meeting", "reminder"],
      task_status: ["due", "progress", "done"],
    },
  },
} as const
