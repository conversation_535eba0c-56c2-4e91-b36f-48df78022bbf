"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Pencil, Trash } from "lucide-react";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

type ClientGroup = {
  id: number;
  name: string;
  totalClients: number;
  discount?: number; // Optional discount field
};

export default function ClientGroupsPage() {
  const [clientGroups, setClientGroups] = useState<ClientGroup[]>([
    { id: 1, name: "VIP Clients", totalClients: 12, discount: 10 },
    { id: 2, name: "Regular Clients", totalClients: 45, discount: 5 },
    { id: 3, name: "New Clients", totalClients: 8, discount: 0 },
  ]);

  const [selectedGroup, setSelectedGroup] = useState<ClientGroup | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [discount, setDiscount] = useState<number>(0);

  const handleDelete = (id: number) => {
    setClientGroups((prev) => prev.filter((group) => group.id !== id));
  };

  const handleView = (group: ClientGroup) => {
    setSelectedGroup(group);
    setIsViewModalOpen(true);
  };

  const handleEdit = (group: ClientGroup) => {
    setSelectedGroup(group);
    setDiscount(group.discount || 0);
    setIsEditModalOpen(true);
  };

  const handleSaveDiscount = () => {
    if (selectedGroup) {
      setClientGroups((prev) =>
        prev.map((group) =>
          group.id === selectedGroup.id ? { ...group, discount } : group
        )
      );
      setIsEditModalOpen(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Client Groups</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Total Clients</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {clientGroups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>{group.name}</TableCell>
                  <TableCell>{group.totalClients}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      {/* View Action */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleView(group)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {/* Edit Discount Action */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(group)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      {/* Delete Action */}
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDelete(group.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>View Client Group</DialogTitle>
          </DialogHeader>
          {selectedGroup && (
            <div className="space-y-4">
              <p>
                <span className="font-medium">Name:</span> {selectedGroup.name}
              </p>
              <p>
                <span className="font-medium">Total Clients:</span>{" "}
                {selectedGroup.totalClients}
              </p>
              <p>
                <span className="font-medium">Discount:</span>{" "}
                {selectedGroup.discount || 0}%
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Discount Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Discount</DialogTitle>
          </DialogHeader>
          {selectedGroup && (
            <div className="space-y-4">
              <p>
                Editing discount for:{" "}
                <span className="font-medium">{selectedGroup.name}</span>
              </p>
              <div>
                <Label>Discount (%)</Label>
                <Input
                  type="number"
                  value={discount}
                  onChange={(e) => setDiscount(Number(e.target.value))}
                  min={0}
                  max={100}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveDiscount}>Save</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}