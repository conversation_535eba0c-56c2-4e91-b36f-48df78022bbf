
"use client"
import React from 'react';
import { BsSlack } from 'react-icons/bs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HiDesktopComputer } from "react-icons/hi";
import Typography from '@/components/ui/typography';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTheme } from "next-themes";
import Link from 'next/link';

const ForgotPassword = () => {
    const { setTheme } = useTheme();

    const formSchema = z.object({
        email: z.string().email({ message: "Please provide a valid email address" }),
    });

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
        },
    });

    const handleSubmit = (data: z.infer<typeof formSchema>) => {
        console.log("Forgot Password Request:", data);
        alert(`Password reset instructions sent to ${data.email}`);
    };

    return (
        <div className="min-h-screen w-full p-4 sm:p-8 grid place-content-center bg-gray-200 dark:bg-gray-900">
            {/* Theme Toggle */}
            <div className="absolute top-5 right-5">
                <div className="flex space-x-2">
                    <button onClick={() => setTheme('light')} aria-label="Light Mode">
                        <HiSun size={24} className="text-yellow-500" />
                    </button>
                    <button onClick={() => setTheme('dark')} aria-label="Dark Mode">
                        <HiMoon size={24} className="text-gray-500" />
                    </button>
                    <button onClick={() => setTheme('system')} aria-label="System Mode">
                        <HiDesktopComputer size={24} className="text-blue-500" />
                    </button>
                </div>
            </div>

            {/* Forgot Password Card */}
            <div className="w-full max-w-md border border-neutral-200 p-5 sm:p-8 rounded-lg shadow-lg bg-gray-100 dark:bg-gray-800">
                {/* Header */}
                <div className="flex justify-center items-center gap-3 mb-6">
                    <BsSlack size={30} className="text-blue-500" />
                    <div className="flex">
                        <Typography text="Seben" variant="h2" />
                        <Typography text="za" variant="h2" className="text-blue-500" />
                    </div>
                </div>

                <Typography
                    text="Forgot Your Password?"
                    variant="h3"
                    className="mb-3 text-center text-gray-800 dark:text-white"
                />
                <Typography
                    text="Enter your email address, and we'll send you instructions to reset your password."
                    variant="p"
                    className="mb-7 text-center text-gray-600 dark:text-gray-400"
                />

                {/* Forgot Password Form */}
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)}>
                        <FormField
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <Typography text="Email" variant="p" className="mb-1 text-left text-gray-700 dark:text-gray-300" />
                                    <FormControl>
                                        <Input
                                            placeholder="<EMAIL>"
                                            {...field}
                                            className="dark:bg-gray-700 dark:text-white"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button
                            variant="secondary"
                            className="w-full mt-6 bg-blue-600 hover:bg-blue-500 dark:bg-blue-600 dark:hover:bg-blue-500 text-white"
                            type="submit"
                        >
                            <Typography text="Send Reset Instructions" variant="p" />
                        </Button>
                    </form>
                </Form>

                {/* Footer */}
                <div className="flex justify-center items-center mt-6">
                    <Typography text="Remembered your password? " variant="p" />
                    <Link href="/auth/login" className="ml-1 underline text-blue-600">
                        Sign in
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
