/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Plus } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { getAllInvoices } from "@/query/invoices";

type Invoice = {
  id: string;
  reference_number: string | null;
  created_at: string;
  total_amount: number;
  status: "pending" | "approved" | "rejected" | string;
  client: string;
};

export default function ManageInvoicesPage() {
  const { companyId } = useParams() as { companyId: string };
  const [quotes, setQuotes] = useState<Invoice[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  const badgeClasses: Record<string, string> = {
    pending:  "bg-yellow-600 text-white",
    approved: "bg-green-600 text-white",
    rejected: "bg-red-600 text-white",
  };

  useEffect(() => {
    if (!companyId) return;
    getAllInvoices(companyId).then(res => {
      if (!res.error) {
        setQuotes(res.data.map((q: any) => ({
          id: q.id,
          reference_number: q.reference_number,
          created_at: q.created_at,
          total_amount: q.totals?.total_after_discount ?? 0,
          status: q.status,
          client: q.clients?.full_name ?? "Unknown",
        })));
      }
    });
  }, [companyId]);

  // filter without toLowerCase, guard against null
  const filtered = quotes.filter(q =>
    (q.reference_number ?? "").includes(searchQuery)
  );

  return (
    <div className="container mx-auto p-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0"></h1>
        <Link href={`/dashboard/${companyId}/invoice`}>
          <Button className="flex items-center">
            <Plus className="h-4 w-4 mr-2" /> New Invoice
          </Button>
        </Link>
      </div>

      {/* Search + Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-center">
            <CardTitle className="mb-4 md:mb-0">Manage Invoices</CardTitle>
            <div className="flex gap-2">
              <Input
                placeholder="Search reference..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full sm:w-64"
              />
              <Button className="flex items-center">
                <Search className="h-4 w-4 mr-2" /> Search
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No.</TableHead>
                  <TableHead>Invoice #</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filtered.length > 0 ? (
                  filtered.map((inv, i) => {
                    const date = new Date(inv.created_at).toLocaleDateString("en-GB", {
                      day: "2-digit", month: "short", year: "numeric"
                    });
                    return (
                      <TableRow key={inv.id}>
                        <TableCell>{i + 1}</TableCell>
                        <TableCell>{inv.reference_number}</TableCell>
                        <TableCell>{inv.client}</TableCell>
                        <TableCell>{date}</TableCell>
                        <TableCell>R{inv.total_amount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge
                            className={badgeClasses[inv.status] ?? "bg-gray-100 text-gray-800"}
                          >
                            {inv.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Link href={`/dashboard/${companyId}/manage-invoice/view/${inv.id}`}>
                            <Button size="sm" variant="outline">View</Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center">
                      No records found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
