import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from '@/provider/theme-provider'
import { Toaster } from "@/components/ui/sonner"

export const metadata: Metadata = {
  title: "Sebenza",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      
      <body className="bg-gray-200 dark:bg-gray-900">
        <ThemeProvider attribute='class' defaultTheme='system' enableSystem disableTransitionOnChange>
          {children}
        </ThemeProvider>
        <Toaster />
      </body>
    </html>
  );
}
