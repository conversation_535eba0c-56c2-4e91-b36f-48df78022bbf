/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useSignupValues } from '@/hooks/signup-values';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Typography from '../ui/typography';
import { NewEmployee, registerEmployees } from '@/actionsApi/register-company';
import { toast } from 'sonner';
import { useState } from 'react';

const formSchema = z.object({
  employees: z
    .array(
      z.object({
        name:       z.string().nonempty("Name is required"),
        surname:    z.string().nonempty("Surname is required"),
        email:      z.string().email("Invalid email address"),
        role:       z.string().nonempty("Role is required"),
        permission: z.string().nonempty("Permission is required"),
        password:   z.string().min(8, "Password must be at least 8 characters long"),
        phone:      z.string().nonempty("Phone is required"),
        gender:     z.enum(['Male','Female','Other'], { errorMap: () => ({ message: 'Select Male, Female or Other' }) }),
        street:     z.string().nonempty("Street address is required"),
        city:       z.string().nonempty("City is required"),
        state:      z.string().nonempty("State is required"),
        zip:        z.string().nonempty("ZIP code is required"),
        country:    z.string().nonempty("Country is required"),
      })
    )
    .max(10, "You cannot add more users than selected")
    .min(1,  "At least one user must be added"),
});

type FormValues = z.infer<typeof formSchema>;

const FourthStepper = () => {
  const { setCurrentStep, updateFormData, formData } = useSignupValues();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const maxUsers = formData.numberOfUsers || 1;
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employees: [
        {
          name: "", surname: "", email: "", role: "", permission: "", password: "",
          phone: "", gender: 'Other',
          street: "", city: "", state: "", zip: "", country: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "employees",
  });

  const handleNext = async (data: FormValues) => {
    setIsAuthenticating(true);
    updateFormData({ ...data, skip: false });

    try {
      const employeesPayload: NewEmployee[] = data.employees.map(emp => ({
        fullName:    `${emp.name} ${emp.surname}`,
        email:       emp.email,
        password:    emp.password,
        phone:       emp.phone,
        gender:      emp.gender,
        street:      emp.street,
        city:        emp.city,
        state:       emp.state,
        zip:         emp.zip,
        country:     emp.country,
      }));

      const response = await registerEmployees({ employees: employeesPayload });
      if (response.error) {
        toast.error(`Error: ${response.error}`);
        setIsAuthenticating(false);
        return;
      }

      const employeeIds = response.employees?.map((e: any) => e.id);
      updateFormData({ employeesIds: employeeIds, skip: false });
      toast.success("Employees registered successfully!");
      setCurrentStep(5);

    } catch (error) {
      console.error("Error registering employees:", error);
      toast.error("An unexpected error occurred while registering employees.");
    } finally {
      setIsAuthenticating(false);
    }
  };

  const handleBack = () => setCurrentStep(3);
  const handleSkip = () => {
    updateFormData({ skip: true });
    setCurrentStep(5);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleNext)}>
        <div className="mb-8">
          <Typography
            text="Step 4: Optional: Add Users"
            variant="h2"
            className="text-2xl text-center font-bold text-blue-600 dark:text-blue-400"
          />
          <Typography
            text={`You can add employees now or later. You can only add up to ${maxUsers} users.`}
            variant="p"
            className="opacity-90 text-gray-600 dark:text-gray-400 text-center mt-4"
          />
          <Typography
            text="If you would like to add more users later, go to My Package and select Add More Users."
            variant="p"
            className="opacity-90 mb-7 text-gray-600 dark:text-gray-400 text-center"
          />
        </div>

        {fields.map((item, idx) => (
          <div
            key={item.id}
            className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow"
          >
            {/* Name */}
            <FormField name={`employees.${idx}.name`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Name</label>
                <FormControl>
                  <Input {...field} placeholder="Enter name" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Surname */}
            <FormField name={`employees.${idx}.surname`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Surname</label>
                <FormControl>
                  <Input {...field} placeholder="Enter surname" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Email */}
            <FormField name={`employees.${idx}.email`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Email</label>
                <FormControl>
                  <Input {...field} type="email" placeholder="Enter email" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Role */}
            <FormField name={`employees.${idx}.role`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Role</label>
                <FormControl>
                  <Input {...field} placeholder="Enter role" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>


            {/* Password */}
            <FormField name={`employees.${idx}.password`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Password</label>
                <FormControl>
                  <Input {...field} type="password" placeholder="Enter password" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Phone */}
            <FormField name={`employees.${idx}.phone`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Phone</label>
                <FormControl>
                  <Input {...field} placeholder="Enter phone" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Gender */}
            <FormField name={`employees.${idx}.gender`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Gender</label>
                <FormControl>
                  <select {...field} className="w-full p-2 border rounded-lg">
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Street */}
            <FormField name={`employees.${idx}.street`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Street</label>
                <FormControl>
                  <Input {...field} placeholder="Enter street address" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* City */}
            <FormField name={`employees.${idx}.city`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">City</label>
                <FormControl>
                  <Input {...field} placeholder="Enter city" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* State */}
            <FormField name={`employees.${idx}.state`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">State</label>
                <FormControl>
                  <Input {...field} placeholder="Enter state/province" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* ZIP */}
            <FormField name={`employees.${idx}.zip`} render={({ field }) => (
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">ZIP Code</label>
                <FormControl>
                  <Input {...field} placeholder="Enter ZIP code" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Country */}
            <FormField name={`employees.${idx}.country`} render={({ field }) => ( 
              <FormItem>
                <label className="text-sm text-gray-700 dark:text-gray-300 mb-1">Country</label>
                <FormControl>
                  <Input {...field} placeholder="Enter country" className="w-full" />
                </FormControl>
                <FormMessage/>
              </FormItem>
            )}/>

            {/* Remove button spans both columns */}
            <div className="col-span-1 sm:col-span-2 text-right">
              {fields.length > 1 && (
                <Button
                  type="button"
                  onClick={() => remove(idx)}
                  className="text-red-500 underline"
                >
                  Remove User
                </Button>
              )}
            </div>
          </div>
        ))}

        {fields.length < maxUsers && (
          <Button
            type="button"
            onClick={() => append({
              name: "", surname: "", email: "", role: "",
              permission: "", password: "", phone: "", gender: "Other",
              street: "", city: "", state: "", zip: "", country: "",
            })}
            className="bg-blue-600 hover:bg-blue-500 w-full my-5 text-white"
          >
            Add Another User
          </Button>
        )}

        <div className="flex justify-between gap-4 mt-6">
          <Button onClick={handleBack} className="bg-gray-400 hover:bg-gray-300 text-white">
            Back
          </Button>
          <div className="flex gap-4 flex-1">
            <Button onClick={handleSkip} className="bg-gray-400 hover:bg-gray-300 text-white flex-1">
              Skip
            </Button>
            <Button
              type="submit"
              disabled={isAuthenticating}
              className="bg-blue-600 hover:bg-blue-500 text-white flex-1"
            >
              {isAuthenticating ? "Registering..." : "Next"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default FourthStepper;
