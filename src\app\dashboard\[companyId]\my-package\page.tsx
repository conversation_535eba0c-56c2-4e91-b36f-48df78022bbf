/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import React, { useState, useEffect } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";

export default function MyPackagePage() {
  // Package information simulation
  const [packageInfo, setPackageInfo] = useState({
    name: "Standard Package",
    currentUsers: 10,
    maxUsers: 15,
    expiryDate: "2023-10-20", // sample expiry date
  });

  // States for adding new users
  const [newUsers, setNewUsers] = useState(0);
  const [paymentFrequency, setPaymentFrequency] = useState("Monthly");

  // State to simulate expired package popup
  const [expired, setExpired] = useState(false);

  // Simulate expiry check (for demo, check if expiry date is before today)
  useEffect(() => {
    const today = new Date();
    const expiry = new Date(packageInfo.expiryDate);
    if (expiry < today) {
      setExpired(true);
    }
  }, [packageInfo.expiryDate]);

  const handleAddUsers = () => {
    // In a real app, you would submit this to your backend or payment gateway.
    alert(`You have selected to add ${newUsers} user(s) on a ${paymentFrequency} payment plan.`);
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Package Overview */}
      <Card>
        <CardHeader>
          <CardTitle>My Package</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>Package Name:</strong> {packageInfo.name}
            </p>
            <p>
              <strong>Current Users:</strong> {packageInfo.currentUsers} / {packageInfo.maxUsers}
            </p>
            <p>
              <strong>Expiry Date:</strong> {packageInfo.expiryDate}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Add More Users Section */}
      <Card>
        <CardHeader>
          <CardTitle>Add More Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block font-medium mb-1">Number of New Users</label>
              <Input
                type="number"
                min={1}
                value={newUsers}
                onChange={(e) => setNewUsers(Number(e.target.value))}
                placeholder="Enter number of new users"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">Payment Frequency</label>
              <Select value={paymentFrequency} onValueChange={(val) => setPaymentFrequency(val)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Payment Frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Monthly">Monthly</SelectItem>
                  <SelectItem value="Quarterly">Quarterly</SelectItem>
                  <SelectItem value="Yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleAddUsers}>Click to Pay</Button>
          </div>
        </CardContent>
      </Card>

      {/* Expiry Pop-Up (Dialog) */}
      {expired && (
        <Dialog open={expired} onOpenChange={setExpired}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Package Expired</DialogTitle>
              <DialogDescription>
                Your package has reached its expiry deadline. Please click below to renew your package.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button onClick={() => alert("Redirecting to payment...")}>Renew Package</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
