/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

// app/dashboard/[companyId]/cases/new/page.tsx
import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getEmployeesByCompany } from "@/query/employees";
import { toast } from "sonner";

import { User } from "@/types/app";
import { CaseInsertParams, insertCase } from "@/query/case_management";

type Employee = { id: string; full_name: string };

export default function AddNewCasePage() {
  const { companyId } = useParams() as { companyId: string };
  const router = useRouter();

  // current user
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  useEffect(() => {
    fetch("/api/getUser")
      .then((res) => res.json())
      .then((data) => {
        if (data.success) setCurrentUser(data.user.user);
      })
      .catch(console.error);
  }, []);

  // form state
  const [caseTitle, setCaseTitle] = useState("");
  const [caseBudget, setCaseBudget] = useState("");
  const [caseDescription, setCaseDescription] = useState("");
  const [caseExpenseBoard, setCaseExpenseBoard] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [status, setStatus] = useState("");
  const [progress, setProgress] = useState("");
  const [priority, setPriority] = useState("");
  const [customerName, setCustomerName] = useState("");
  const [customerCanView, setCustomerCanView] = useState(false);
  const [customerCanComment, setCustomerCanComment] = useState(false);
  const [assignedTo, setAssignedTo] = useState("");
  const [taskCommunication, setTaskCommunication] = useState("");
  const [phases, setPhases] = useState("");
  const [ganttCharts, setGanttCharts] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  type ExpenseItem = {
    id: number;
    description: string;
    amount: number;
    whoSpent: string;
    date: string;
  };

    // Case Expense Board state
    const [expenseItems, setExpenseItems] = useState<ExpenseItem[]>([
      { id: 1, description: "", amount: 0, whoSpent: "", date: "" },
    ]);

    const handleAddExpenseItem = () => {
      setExpenseItems((prev) => [
        ...prev,
        { id: prev.length + 1, description: "", amount: 0, whoSpent: "", date: "" },
      ]);
    };
  
    const handleRemoveExpenseItem = (id: number) => {
      setExpenseItems((prev) => prev.filter((item) => item.id !== id));
    };
  
    const handleExpenseItemChange = (
      id: number,
      field: keyof ExpenseItem,
      value: string | number
    ) => {
      setExpenseItems((prev) =>
        prev.map((item) =>
          item.id === id ? { ...item, [field]: typeof value === "string" ? value : Number(value) } : item
        )
      );
    };

  // employees dropdown
  const [employees, setEmployees] = useState<Employee[]>([]);
  useEffect(() => {
    if (!companyId) return;
    getEmployeesByCompany(companyId).then(({ data, error }) => {
      if (data) setEmployees(data);
      else console.error(error);
    });
  }, [companyId]);

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setUploadedFiles((prevFiles) => [...prevFiles, ...newFiles]);
    }
  };

  const handleRemoveFile = (index: number) => {
    setUploadedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  // insert on submit
  const handleSubmit = async () => {
    if (!currentUser) {
      toast.error("You must be logged in to create a case.");
      return;
    }

    setIsSubmitting(true);

    const payload: CaseInsertParams = {
      company_id: companyId,
      case_title: caseTitle,
      case_budget: caseBudget ? parseFloat(caseBudget) : 0,
      case_description: caseDescription,
      case_expense_board: caseExpenseBoard,
      start_date: startDate,
      end_date: endDate,
      status,
      progress: progress ? parseFloat(progress) : 0,
      priority,
      customer_name: customerName,
      customer_can_view: customerCanView,
      customer_can_comment: customerCanComment,
      assigned_to: assignedTo,
      task_communication: taskCommunication,
      phases,
      gantt_charts: ganttCharts,
      created_by: currentUser.id,
      updated_by: currentUser.id,
    };

    try {
      const { data, error } = await insertCase(payload);

      if (error) {
        console.error("Insert failed:", error);
        toast.error("Failed to create case. Please try again.");
      } else {
        toast.success("Case created successfully!");
        router.push(`/dashboard/${companyId}/case-management`);
      }
    } catch (err) {
      console.error(err);
      toast.error("An unexpected error occurred.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add a New Case</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Case Title */}
            <div>
              <Label>Case Title</Label>
              <Input
                value={caseTitle}
                onChange={(e) => setCaseTitle(e.target.value)}
                placeholder="Enter case title"
              />
            </div>

            {/* Case Budget */}
            <div>
              <Label>Case Budget</Label>
              <Input
                type="number"
                value={caseBudget}
                onChange={(e) => setCaseBudget(e.target.value)}
                placeholder="Budget amount"
              />
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <Label>Case Description</Label>
              <Textarea
                value={caseDescription}
                onChange={(e) => setCaseDescription(e.target.value)}
                placeholder="Describe the case"
              />
            </div>

          {/* Case Expense Board */}
          <div className="border p-4 space-y-4">
            <h3 className="text-lg font-bold">Case Expense Board</h3>
            {expenseItems.map((item, index) => (
              <div key={item.id} className="grid grid-cols-5 gap-4">
                <div>
                  <Label>Description</Label>
                  <Input
                    value={item.description}
                    onChange={(e) =>
                      handleExpenseItemChange(item.id, "description", e.target.value)
                    }
                    placeholder="Expense description"
                  />
                </div>
                <div>
                  <Label>Amount</Label>
                  <Input
                    type="number"
                    value={item.amount}
                    onChange={(e) =>
                      handleExpenseItemChange(item.id, "amount", e.target.value)
                    }
                    placeholder="Amount"
                  />
                </div>
                <div>
                  <Label>Who Spent</Label>
                  <Input
                    value={item.whoSpent}
                    onChange={(e) =>
                      handleExpenseItemChange(item.id, "whoSpent", e.target.value)
                    }
                    placeholder="Who spent"
                  />
                </div>
                <div>
                  <Label>Date</Label>
                  <Input
                    type="date"
                    value={item.date}
                    onChange={(e) =>
                      handleExpenseItemChange(item.id, "date", e.target.value)
                    }
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleRemoveExpenseItem(item.id)}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            ))}
            <Button type="button" onClick={handleAddExpenseItem}>
              + Add Expense Item
            </Button>
          </div>

          <div className="md:col-span-2">
          <Label>Upload Files</Label>
          <Input
            type="file"
            multiple
            accept=".pdf,.docx,.jpg,.png"
            onChange={handleFileUpload}
          />
          {uploadedFiles.length > 0 && (
            <ul className="mt-2 space-y-1 text-sm text-gray-700 dark:text-gray-300">
              {uploadedFiles.map((file, index) => (
                <li key={index} className="flex justify-between items-center border-b pb-1">
                  {file.name}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveFile(index)}
                    className="text-red-500"
                  >
                    Remove
                  </Button>
                </li>
              ))}
            </ul>
          )}
        </div>
          

            {/* Dates */}
            <div>
              <Label>Start Date</Label>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            <div>
              <Label>End Date</Label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>

            {/* Status */}
            <div>
              <Label>Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {[
                    "pending",
                    "in_progress",
                    "halfway",
                    "almost_done",
                    "completed",
                    "on_pause",
                    "cancelled",
                  ].map((opt) => (
                    <SelectItem key={opt} value={opt}>
                      {opt.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Progress */}
            <div>
              <Label>Progress (%)</Label>
              <Input
                type="number"
                min={0}
                max={100}
                step={1}
                value={progress}
                onChange={(e) => setProgress(e.target.value)}
              />
            </div>

            {/* Priority */}
            <div>
              <Label>Priority</Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {["low", "medium", "high", "urgent"].map((opt) => (
                    <SelectItem key={opt} value={opt}>
                      {opt.charAt(0).toUpperCase() + opt.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Customer Name */}
            <div>
              <Label>Customer Name</Label>
              <Input
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="Select or enter customer"
              />
            </div>

            {/* Customer Can View */}
            <div className="flex items-center space-x-4">
              <Label>Customer Can View</Label>
              <Switch
                checked={customerCanView}
                onCheckedChange={setCustomerCanView}
              />
            </div>

            {/* Customer Can Comment */}
            <div className="flex items-center space-x-4">
              <Label>Customer Can Comment</Label>
              <Switch
                checked={customerCanComment}
                onCheckedChange={setCustomerCanComment}
              />
            </div>

            {/* Assign To */}
            <div>
              <Label>Assign To</Label>
              <Select value={assignedTo} onValueChange={setAssignedTo}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an employee" />
                </SelectTrigger>
                <SelectContent>
                  {employees.map((emp) => (
                    <SelectItem key={emp.id} value={emp.id}>
                      {emp.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Task Communication */}
            <div>
              <Label>Task Communication</Label>
              <Select
                value={taskCommunication}
                onValueChange={setTaskCommunication}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="team">Emails to Team</SelectItem>
                  <SelectItem value="team_customers">
                    Emails to Team and Customers
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Phases */}
            <div className="md:col-span-2">
              <Label>Phases</Label>
              <Textarea
                value={phases}
                onChange={(e) => setPhases(e.target.value)}
                placeholder="Enter phase labels (e.g. A, B, C)"
              />
            </div>

            {/* Gantt Charts */}
            <div className="md:col-span-2">
              <Label>Gantt Charts</Label>
              <Textarea
                value={ganttCharts}
                onChange={(e) => setGanttCharts(e.target.value)}
                placeholder="Add Gantt chart notes or links"
              />
            </div>
          </div>

          <Button
            onClick={handleSubmit}
            className="mt-4"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Case"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
