/* eslint-disable @typescript-eslint/no-unused-vars */

"use client";
import React, { useState, useEffect } from "react";
import { BsSlack } from "react-icons/bs";
import { FcGoogle } from "react-icons/fc";
import { Hi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, HiDesktopComputer } from "react-icons/hi";
import Typography from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTheme } from "next-themes";
import Link from "next/link";
import { Provider } from "@supabase/supabase-js";
import { supabaseBrowserClient } from "@/supabase/supbaseClient";
import { redirect, useRouter } from "next/navigation";
import { login } from "@/actionsApi/login";
import { toast } from "sonner";
import { User } from "@/types/app";
import { useUserProfile } from "@/query/user";

const Login = () => {
  const { setTheme } = useTheme();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const router = useRouter();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  

  const formSchema = z.object({
    email: z.string().email({ message: "Please provide a valid email address" }),
    password: z.string().nonempty("Password is required"),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsAuthenticating(true);
    

    try {
      const response = await login(values);
      const { data, error } = JSON.parse(response);

      

      if (error) {
        if (error.message === "Invalid login credentials") {
          toast.error("Invalid email or password. Please try again.");
          setIsAuthenticating(false);
        } else {
          toast.error("User does not exist. Please sign up first.");
          setIsAuthenticating(false);
        }
        return;
      }
      if(data){
        toast.success('Login successful! Redirecting...');
        form.reset();
        router.push("/");
      }
      
    } catch (err) {
      setIsAuthenticating(false);
      console.error('Error during registration:', err);
      toast.error("An unexpected error occurred. Please try again later.");
    }
  }

  async function socialAuth(provider: Provider) {
    setIsAuthenticating(true);
    await supabaseBrowserClient.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${location.origin}/auth/callback`,
      },
    });
    setIsAuthenticating(false);
  }

  const { profile, loading } = useUserProfile(currentUser?.id, "");
  
    
    useEffect(() => {
      const fetchUser = async () => {
        try {
          const response = await fetch("/api/getUser");
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          const data = await response.json();
          console.log("Trying", data);
  
          if (data.success) {
            setCurrentUser(data.user.user);
          } else {
            console.error("User fetch error:", data.error);
          }
        } catch (error) {
          console.error("Error fetching user:", error);
        } 
      };
  
      fetchUser();
    }, []);

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 border-solid"></div>
        </div>
      );
    }

    if(profile){
      redirect(`/dashboard/${profile.company_id}`)
    }


  return (
    <div className="min-h-screen w-full p-4 sm:p-8 grid place-content-center bg-gray-200 dark:bg-gray-900">
      {/* Theme Toggle */}
      <div className="absolute top-5 right-5">
        <div className="flex space-x-2">
          <button onClick={() => setTheme("light")} aria-label="Light Mode">
            <HiSun size={24} className="text-yellow-500" />
          </button>
          <button onClick={() => setTheme("dark")} aria-label="Dark Mode">
            <HiMoon size={24} className="text-gray-500" />
          </button>
          <button onClick={() => setTheme("system")} aria-label="System Mode">
            <HiDesktopComputer size={24} className="text-blue-500" />
          </button>
        </div>
      </div>

      {/* Login Card */}
      <div className="w-full max-w-md md:max-w-lg lg:max-w-xl border border-neutral-200 p-5 sm:p-8 rounded-lg shadow-lg bg-gray-100 dark:bg-gray-800">
        {/* Header */}
        <div className="flex justify-center items-center gap-3 mb-6">
          <BsSlack size={30} className="text-blue-500" />
          <div className="flex">
            <Typography text="Seben" variant="h2" />
            <Typography text="za" variant="h2" className="text-blue-500" />
          </div>
        </div>

        <Typography
          text="Sign in to your account"
          variant="h3"
          className="mb-3 text-center text-gray-800 dark:text-white"
        />
        <Typography
          text="Login in and continue your journey with the best Sebenza platform."
          variant="p"
          className="mb-7 text-center text-gray-600 dark:text-gray-400"
        />

       
        {/* Social Login */}
        <div className="flex flex-col space-y-4">
          <Button 
            variant="outline" 
            className="py-3 border flex space-x-3 items-center" 
            //onClick={() => socialAuth('google')}
            >
            <FcGoogle size={24} />
            <Typography text="Sign in with Google" variant="p" className="text-base" />
          </Button>
        </div>

        {/* Divider */}
        <div className="flex items-center my-6">
          <div className="flex-1 border-t border-neutral-300" />
          <Typography text="OR" variant="p" className="mx-4 text-sm text-gray-600 dark:text-gray-400" />
          <div className="flex-1 border-t border-neutral-300" />
        </div>

        {/* Login Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              name="email"
              render={({ field }) => (
                <FormItem>
                  <Typography text="Email" variant="p" className="mb-1 text-left text-gray-700 dark:text-gray-300" />
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      {...field}
                      className="dark:bg-gray-700 dark:text-white"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="password"
              render={({ field }) => (
                <FormItem>
                  <Typography
                    text="Password"
                    variant="p"
                    className="mt-4 mb-1 text-left text-gray-700 dark:text-gray-300"
                  />
                  <FormControl>
                    <Input
                      placeholder="***********"
                      {...field}
                      type="password"
                      className="dark:bg-gray-700 dark:text-white"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Forgot Password Link */}
            <div className="mt-2 mb-6 text-right">
              <Link href="/auth/forgot-password" className="text-sm underline text-blue-600">
                Forgot Password?
              </Link>
            </div>

            <Button
              variant="secondary"
              className="w-full bg-blue-600 hover:bg-blue-500 dark:bg-blue-600 dark:hover:bg-blue-500 text-white"
              type="submit"
              disabled={isAuthenticating}
            >
              {isAuthenticating ? "Signing in..." : "Sign in"}
            </Button>
          </form>
        </Form>

        {/* Footer */}
        <div className="flex justify-center items-center mt-6">
          <Typography text="Don&apos;t have an account? " variant="p" />
          <Link href="/auth/signup" className="ml-1 underline text-blue-600">
            Sign up
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Login;
