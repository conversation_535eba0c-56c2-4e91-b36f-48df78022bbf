import React, { useState } from 'react';
import { BankReconciliation, BankTransaction, JournalEntry } from '@/types/accounting';
import { useToast } from '@/hooks/use-toast';

interface BankReconciliationSectionProps {
  // Only pass essential data and minimal callbacks
  journalEntries: JournalEntry[]; // For matching with bank transactions
}

const BankReconciliationSection: React.FC<BankReconciliationSectionProps> = ({ journalEntries }) => {
  const { toast } = useToast();

  // Move all bank reconciliation-related state here
  const [reconciliations, setReconciliations] = useState<BankReconciliation[]>([]);
  const [selectedReconciliation, setSelectedReconciliation] = useState<BankReconciliation | null>(null);
  const [showReconciliationDialog, setShowReconciliationDialog] = useState(false);
  const [showImportStatementDialog, setShowImportStatementDialog] = useState(false);
  const [showMatchDialog, setShowMatchDialog] = useState(false);
  const [bankTransactions, setBankTransactions] = useState<BankTransaction[]>([]);
  const [selectedBankTransaction, setSelectedBankTransaction] = useState<BankTransaction | null>(null);
  const [selectedBookEntry, setSelectedBookEntry] = useState<JournalEntry | null>(null);

  const [reconciliationForm, setReconciliationForm] = useState({
    bankAccount: '',
    statementDate: '',
    openingBalance: '',
    closingBalance: '',
    statementFile: null as File | null
  });

  // Bank Reconciliation Handlers
  const handleStartReconciliation = () => {
    // Form validation
    if (!reconciliationForm.bankAccount.trim()) {
      toast({
        title: "Validation Error",
        description: "Bank account is required.",
        variant: "destructive",
      });
      return;
    }

    if (!reconciliationForm.statementDate.trim()) {
      toast({
        title: "Validation Error",
        description: "Statement date is required.",
        variant: "destructive",
      });
      return;
    }

    if (!reconciliationForm.openingBalance.trim() || !reconciliationForm.closingBalance.trim()) {
      toast({
        title: "Validation Error",
        description: "Opening and closing balances are required.",
        variant: "destructive",
      });
      return;
    }

    try {
      const newReconciliation: BankReconciliation = {
        id: Date.now().toString(),
        reconciliationNumber: `BR-2024-${String(reconciliations.length + 1).padStart(3, '0')}`,
        bankAccount: reconciliationForm.bankAccount.trim(),
        statementDate: new Date(reconciliationForm.statementDate),
        openingBalance: parseFloat(reconciliationForm.openingBalance),
        closingBalance: parseFloat(reconciliationForm.closingBalance),
        status: 'in_progress',
        createdBy: 'Current User',
        createdAt: new Date(),
        matchedTransactions: [],
        unmatchedBankTransactions: [],
        unmatchedBookEntries: []
      };

      setReconciliations([...reconciliations, newReconciliation]);
      setSelectedReconciliation(newReconciliation);

      toast({
        title: "Success",
        description: "Bank reconciliation started successfully!",
      });

      // Reset form and close dialog
      setReconciliationForm({
        bankAccount: '',
        statementDate: '',
        openingBalance: '',
        closingBalance: '',
        statementFile: null
      });
      setShowReconciliationDialog(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start reconciliation. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleImportBankStatement = (file: File) => {
    if (!file) {
      toast({
        title: "Validation Error",
        description: "Please select a file to import.",
        variant: "destructive",
      });
      return;
    }

    // Simulate file processing
    toast({
      title: "Import Started",
      description: "Processing bank statement file...",
    });

    // In a real app, this would parse the file and extract transactions
    setTimeout(() => {
      const mockTransactions: BankTransaction[] = [
        {
          id: '1',
          date: new Date(),
          description: 'Payment from Customer A',
          amount: 1500.00,
          type: 'credit',
          reference: 'REF001',
          isMatched: false
        },
        {
          id: '2',
          date: new Date(),
          description: 'Office Supplies',
          amount: -250.00,
          type: 'debit',
          reference: 'REF002',
          isMatched: false
        }
      ];

      setBankTransactions(mockTransactions);
      toast({
        title: "Success",
        description: `Imported ${mockTransactions.length} transactions from bank statement.`,
      });
      setShowImportStatementDialog(false);
    }, 2000);
  };

  const handleMatchTransaction = (bankTransaction: BankTransaction, bookEntry: JournalEntry) => {
    // Update bank transaction as matched
    const updatedBankTransactions = bankTransactions.map(bt =>
      bt.id === bankTransaction.id
        ? { ...bt, isMatched: true, matchedEntryId: bookEntry.id }
        : bt
    );
    setBankTransactions(updatedBankTransactions);

    // Update reconciliation with matched transaction
    if (selectedReconciliation) {
      const updatedReconciliation = {
        ...selectedReconciliation,
        matchedTransactions: [
          ...selectedReconciliation.matchedTransactions,
          {
            bankTransactionId: bankTransaction.id,
            journalEntryId: bookEntry.id,
            matchedAt: new Date(),
            matchedBy: 'Current User'
          }
        ]
      };
      setSelectedReconciliation(updatedReconciliation);

      const updatedReconciliations = reconciliations.map(r =>
        r.id === selectedReconciliation.id ? updatedReconciliation : r
      );
      setReconciliations(updatedReconciliations);
    }

    toast({
      title: "Success",
      description: "Transaction matched successfully!",
    });
    setShowMatchDialog(false);
  };

  const handleUnmatchTransaction = (bankTransaction: BankTransaction) => {
    // Update bank transaction as unmatched
    const updatedBankTransactions = bankTransactions.map(bt =>
      bt.id === bankTransaction.id
        ? { ...bt, isMatched: false, matchedEntryId: undefined }
        : bt
    );
    setBankTransactions(updatedBankTransactions);

    // Update reconciliation to remove matched transaction
    if (selectedReconciliation) {
      const updatedReconciliation = {
        ...selectedReconciliation,
        matchedTransactions: selectedReconciliation.matchedTransactions.filter(
          mt => mt.bankTransactionId !== bankTransaction.id
        )
      };
      setSelectedReconciliation(updatedReconciliation);

      const updatedReconciliations = reconciliations.map(r =>
        r.id === selectedReconciliation.id ? updatedReconciliation : r
      );
      setReconciliations(updatedReconciliations);
    }

    toast({
      title: "Success",
      description: "Transaction unmatched successfully!",
    });
  };

  const handleCompleteReconciliation = (reconciliationId: string) => {
    const reconciliation = reconciliations.find(r => r.id === reconciliationId);
    if (!reconciliation) return;

    // Check if all transactions are matched
    const unmatchedTransactions = bankTransactions.filter(bt => !bt.isMatched);
    if (unmatchedTransactions.length > 0) {
      toast({
        title: "Cannot Complete",
        description: `${unmatchedTransactions.length} transactions are still unmatched.`,
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to complete reconciliation "${reconciliation.reconciliationNumber}"?`)) {
      const updatedReconciliations = reconciliations.map(r =>
        r.id === reconciliationId
          ? { ...r, status: 'completed' as const, completedAt: new Date() }
          : r
      );
      setReconciliations(updatedReconciliations);

      toast({
        title: "Success",
        description: "Bank reconciliation completed successfully!",
      });
    }
  };

  const handleDeleteReconciliation = (reconciliationId: string) => {
    const reconciliation = reconciliations.find(r => r.id === reconciliationId);
    if (!reconciliation) return;

    if (reconciliation.status === 'completed') {
      toast({
        title: "Cannot Delete",
        description: "Cannot delete completed reconciliation.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to delete reconciliation "${reconciliation.reconciliationNumber}"?`)) {
      const updatedReconciliations = reconciliations.filter(r => r.id !== reconciliationId);
      setReconciliations(updatedReconciliations);

      toast({
        title: "Success",
        description: "Bank reconciliation deleted successfully!",
      });
    }
  };

  const handleCancelReconciliation = (reconciliationId: string) => {
    const reconciliation = reconciliations.find(r => r.id === reconciliationId);
    if (!reconciliation) return;

    if (reconciliation.status === 'completed') {
      toast({
        title: "Cannot Cancel",
        description: "Cannot cancel completed reconciliation.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to cancel reconciliation "${reconciliation.reconciliationNumber}"?`)) {
      const updatedReconciliations = reconciliations.map(r =>
        r.id === reconciliationId
          ? { ...r, status: 'cancelled' as const }
          : r
      );
      setReconciliations(updatedReconciliations);

      toast({
        title: "Success",
        description: "Bank reconciliation cancelled successfully!",
      });
    }
  };

  const getUnmatchedBankTransactions = () => {
    return bankTransactions.filter(bt => !bt.isMatched);
  };

  const getUnmatchedBookEntries = () => {
    // Filter journal entries that haven't been matched yet
    const matchedEntryIds = bankTransactions
      .filter(bt => bt.isMatched && bt.matchedEntryId)
      .map(bt => bt.matchedEntryId);

    return journalEntries.filter(entry =>
      entry.status === 'posted' && !matchedEntryIds.includes(entry.id)
    );
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Bank Reconciliation</h2>

      {/* Reconciliations List */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Reconciliations</h3>
        <table className="min-w-full bg-white border">
          <thead>
            <tr>
              <th>Reconciliation #</th>
              <th>Bank Account</th>
              <th>Statement Date</th>
              <th>Opening Balance</th>
              <th>Closing Balance</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {reconciliations.map(rec => (
              <tr key={rec.id}>
                <td>{rec.reconciliationNumber}</td>
                <td>{rec.bankAccount}</td>
                <td>{rec.statementDate.toLocaleDateString()}</td>
                <td>{rec.openingBalance}</td>
                <td>{rec.closingBalance}</td>
                <td>{rec.status}</td>
                <td>
                  <button onClick={() => handleCompleteReconciliation(rec.id)}>Complete</button>
                  <button onClick={() => handleCancelReconciliation(rec.id)}>Cancel</button>
                  <button onClick={() => handleDeleteReconciliation(rec.id)}>Delete</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Bank Transactions */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Bank Transactions</h3>
        <table className="min-w-full bg-white border">
          <thead>
            <tr>
              <th>Date</th>
              <th>Description</th>
              <th>Amount</th>
              <th>Type</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {bankTransactions.map(transaction => (
              <tr key={transaction.id}>
                <td>{transaction.date.toLocaleDateString()}</td>
                <td>{transaction.description}</td>
                <td>{transaction.amount}</td>
                <td>{transaction.type}</td>
                <td>{transaction.isMatched ? 'Matched' : 'Unmatched'}</td>
                <td>
                  {transaction.isMatched ? (
                    <button onClick={() => handleUnmatchTransaction(transaction)}>Unmatch</button>
                  ) : (
                    <button onClick={() => {
                      setSelectedBankTransaction(transaction);
                      setShowMatchDialog(true);
                    }}>Match</button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add dialogs, forms, transaction matching UI, etc. as needed */}
    </div>
  );
};

export default BankReconciliationSection;