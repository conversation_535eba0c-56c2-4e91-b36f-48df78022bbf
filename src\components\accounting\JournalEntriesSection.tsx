import React, { useState } from 'react';
import { JournalEntry, JournalEntryLine } from '@/types/accounting';
import { formatCurrency } from './utils';
import { useToast } from '@/hooks/use-toast';

interface JournalEntriesSectionProps {
  // Only pass essential data and minimal callbacks
  accounts: any[]; // For account selection in forms
}

const JournalEntriesSection: React.FC<JournalEntriesSectionProps> = ({ accounts }) => {
  const { toast } = useToast();

  // Move all journal-related state here
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [editingTransaction, setEditingTransaction] = useState<JournalEntry | null>(null);
  const [showJournalDialog, setShowJournalDialog] = useState(false);
  const [selectedJournalIds, setSelectedJournalIds] = useState<string[]>([]);
  const [selectAllJournals, setSelectAllJournals] = useState(false);
  const [showJournalDetailsDialog, setShowJournalDetailsDialog] = useState(false);
  const [showJournalPrintDialog, setShowJournalPrintDialog] = useState(false);
  const [showReverseEntryDialog, setShowReverseEntryDialog] = useState(false);
  const [selectedJournalEntry, setSelectedJournalEntry] = useState<JournalEntry | null>(null);
  const [journalSearchTerm, setJournalSearchTerm] = useState('');
  const [journalFilterStatus, setJournalFilterStatus] = useState<'all' | 'draft' | 'posted' | 'reversed'>('all');
  const [journalSortBy, setJournalSortBy] = useState<'date' | 'amount' | 'entry'>('date');
  const [journalSortOrder, setJournalSortOrder] = useState<'asc' | 'desc'>('desc');

  const [journalForm, setJournalForm] = useState({
    date: new Date(),
    description: '',
    reference: '',
    lines: [] as Omit<JournalEntryLine, 'id'>[]
  });

  // Enhanced Journal Entry CRUD Handlers
  const handleSaveJournalEntry = () => {
    // Form validation
    if (!journalForm.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Journal entry description is required.",
        variant: "destructive",
      });
      return;
    }

    if (journalForm.lines.length === 0) {
      toast({
        title: "Validation Error",
        description: "At least one journal entry line is required.",
        variant: "destructive",
      });
      return;
    }

    // Validate each line
    for (let i = 0; i < journalForm.lines.length; i++) {
      const line = journalForm.lines[i];
      if (!line.accountId) {
        toast({
          title: "Validation Error",
          description: `Line ${i + 1}: Please select an account.`,
          variant: "destructive",
        });
        return;
      }
      if (!line.description.trim()) {
        toast({
          title: "Validation Error",
          description: `Line ${i + 1}: Line description is required.`,
          variant: "destructive",
        });
        return;
      }
      if (line.debitAmount === 0 && line.creditAmount === 0) {
        toast({
          title: "Validation Error",
          description: `Line ${i + 1}: Either debit or credit amount must be greater than zero.`,
          variant: "destructive",
        });
        return;
      }
    }

    // Check if debits equal credits
    const totalDebits = journalForm.lines.reduce((sum, line) => sum + line.debitAmount, 0);
    const totalCredits = journalForm.lines.reduce((sum, line) => sum + line.creditAmount, 0);

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      toast({
        title: "Validation Error",
        description: "Total debits must equal total credits.",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingTransaction) {
        // Update existing entry
        const updatedEntries = journalEntries.map(entry =>
          entry.id === editingTransaction.id
            ? {
                ...entry,
                date: journalForm.date,
                description: journalForm.description.trim(),
                reference: journalForm.reference.trim(),
                totalDebit: totalDebits,
                totalCredit: totalCredits,
                lines: journalForm.lines.map((line, idx) => ({
                  ...line,
                  id: `${editingTransaction.id}-line-${idx}`,
                  description: line.description.trim()
                }))
              }
            : entry
        );
        setJournalEntries(updatedEntries);
        toast({
          title: "Success",
          description: "Journal entry updated successfully!",
        });
      } else {
        // Create new entry
        const newEntry: JournalEntry = {
          id: Date.now().toString(),
          entryNumber: `JE-2024-${String(journalEntries.length + 1).padStart(3, '0')}`,
          date: journalForm.date,
          description: journalForm.description.trim(),
          reference: journalForm.reference.trim(),
          totalDebit: totalDebits,
          totalCredit: totalCredits,
          status: 'draft',
          createdBy: 'Current User',
          createdAt: new Date(),
          lines: journalForm.lines.map((line, idx) => ({
            ...line,
            id: `${Date.now()}-line-${idx}`,
            description: line.description.trim()
          }))
        };
        setJournalEntries([...journalEntries, newEntry]);
        toast({
          title: "Success",
          description: "Journal entry created successfully!",
        });
      }

      // Reset form and close dialog
      setJournalForm({
        date: new Date(),
        description: '',
        reference: '',
        lines: []
      });
      setEditingTransaction(null);
      setShowJournalDialog(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save journal entry. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditJournalEntry = (entry: JournalEntry) => {
    if (entry.status === 'posted') {
      toast({
        title: "Cannot Edit",
        description: "Cannot edit a posted journal entry. Please reverse the entry to make changes.",
        variant: "destructive",
      });
      return;
    }

    setEditingTransaction(entry);
    setJournalForm({
      date: entry.date,
      description: entry.description,
      reference: entry.reference || '',
      lines: entry.lines.map(line => ({
        accountId: line.accountId,
        accountCode: line.accountCode,
        accountName: line.accountName,
        description: line.description,
        debitAmount: line.debitAmount,
        creditAmount: line.creditAmount
      }))
    });
    setShowJournalDialog(true);
  };

  const handleDeleteJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    // Check if entry is posted
    if (entry.status === 'posted') {
      toast({
        title: "Cannot Delete",
        description: "Cannot delete a posted journal entry. Please reverse the entry instead.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to delete journal entry "${entry.entryNumber}"?`)) {
      try {
        const updatedEntries = journalEntries.filter(e => e.id !== entryId);
        setJournalEntries(updatedEntries);
        toast({
          title: "Success",
          description: "Journal entry deleted successfully!",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete journal entry. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleReverseJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    if (entry.status === 'reversed') {
      toast({
        title: "Already Reversed",
        description: "This journal entry has already been reversed.",
        variant: "destructive",
      });
      return;
    }

    if (entry.status !== 'posted') {
      toast({
        title: "Cannot Reverse",
        description: "Only posted journal entries can be reversed.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to reverse journal entry "${entry.entryNumber}"?`)) {
      try {
        // Mark original entry as reversed
        const updatedEntries = journalEntries.map(e =>
          e.id === entryId
            ? { ...e, status: 'reversed' as const }
            : e
        );

        // Create reversing entry
        const reversingEntry: JournalEntry = {
          id: Date.now().toString(),
          entryNumber: `JE-2024-${String(journalEntries.length + 1).padStart(3, '0')}`,
          date: new Date(),
          description: `REVERSAL: ${entry.description}`,
          reference: `REV-${entry.entryNumber}`,
          totalDebit: entry.totalCredit,
          totalCredit: entry.totalDebit,
          status: 'posted',
          createdBy: 'Current User',
          createdAt: new Date(),
          lines: entry.lines.map((line, idx) => ({
            ...line,
            id: `${Date.now()}-rev-line-${idx}`,
            description: `REVERSAL: ${line.description}`,
            debitAmount: line.creditAmount,
            creditAmount: line.debitAmount
          }))
        };

        setJournalEntries([...updatedEntries, reversingEntry]);
        toast({
          title: "Success",
          description: "Journal entry reversed successfully! Reversing entry created.",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to reverse journal entry. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handlePostJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    if (entry.status !== 'draft') {
      toast({
        title: "Cannot Post",
        description: "Only draft journal entries can be posted.",
        variant: "destructive",
      });
      return;
    }

    // Validate entry before posting
    if (Math.abs(entry.totalDebit - entry.totalCredit) > 0.01) {
      toast({
        title: "Cannot Post",
        description: "Total debits must equal total credits before posting.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Are you sure you want to post journal entry "${entry.entryNumber}"?`)) {
      try {
        const updatedEntries = journalEntries.map(e =>
          e.id === entryId
            ? { ...e, status: 'posted' as const }
            : e
        );
        setJournalEntries(updatedEntries);
        toast({
          title: "Success",
          description: "Journal entry posted successfully!",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to post journal entry. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleViewJournalDetails = (entry: JournalEntry) => {
    setSelectedJournalEntry(entry);
    setShowJournalDetailsDialog(true);
  };

  const handlePrintJournalEntry = (entry: JournalEntry) => {
    setSelectedJournalEntry(entry);
    setShowJournalPrintDialog(true);
  };

  const handleBulkJournalAction = (action: 'post' | 'delete' | 'export', entryIds: string[]) => {
    if (entryIds.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select journal entries to perform bulk actions.",
        variant: "destructive",
      });
      return;
    }

    const confirmMessage = `Are you sure you want to ${action} ${entryIds.length} journal entries?`;
    if (!confirm(confirmMessage)) return;

    try {
      let updatedEntries = [...journalEntries];

      switch (action) {
        case 'post':
          const cannotPost = journalEntries.filter(entry =>
            entryIds.includes(entry.id) && entry.status !== 'draft'
          );

          if (cannotPost.length > 0) {
            toast({
              title: "Cannot Post",
              description: `${cannotPost.length} entries cannot be posted (not in draft status).`,
              variant: "destructive",
            });
            return;
          }

          updatedEntries = journalEntries.map(entry =>
            entryIds.includes(entry.id) && entry.status === 'draft'
              ? { ...entry, status: 'posted' as const }
              : entry
          );
          break;

        case 'delete':
          const cannotDelete = journalEntries.filter(entry =>
            entryIds.includes(entry.id) && entry.status === 'posted'
          );

          if (cannotDelete.length > 0) {
            toast({
              title: "Cannot Delete",
              description: `${cannotDelete.length} entries cannot be deleted (already posted).`,
              variant: "destructive",
            });
            return;
          }

          updatedEntries = journalEntries.filter(entry =>
            !entryIds.includes(entry.id) || entry.status === 'posted'
          );
          break;

        case 'export':
          const entriesToExport = journalEntries.filter(entry => entryIds.includes(entry.id));
          const csvContent = [
            ['Entry Number', 'Date', 'Description', 'Reference', 'Debit', 'Credit', 'Status'].join(','),
            ...entriesToExport.map(entry => [
              entry.entryNumber,
              entry.date.toLocaleDateString(),
              `"${entry.description}"`,
              `"${entry.reference || ''}"`,
              entry.totalDebit,
              entry.totalCredit,
              entry.status
            ].join(','))
          ].join('\n');

          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `journal-entries-${new Date().toISOString().split('T')[0]}.csv`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);

          toast({
            title: "Success",
            description: `${entryIds.length} journal entries exported successfully!`,
          });
          return;
      }

      setJournalEntries(updatedEntries);
      setSelectedJournalIds([]);
      setSelectAllJournals(false);

      toast({
        title: "Success",
        description: `${entryIds.length} journal entries ${action}ed successfully!`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to perform bulk action. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter and sort journal entries
  const getFilteredJournalEntries = () => {
    const filtered = journalEntries.filter(entry => {
      const matchesSearch = entry.description.toLowerCase().includes(journalSearchTerm.toLowerCase()) ||
                           entry.entryNumber.toLowerCase().includes(journalSearchTerm.toLowerCase()) ||
                           entry.reference?.toLowerCase().includes(journalSearchTerm.toLowerCase()) ||
                           entry.lines.some(line => line.accountName.toLowerCase().includes(journalSearchTerm.toLowerCase()));
      const matchesStatus = journalFilterStatus === 'all' || entry.status === journalFilterStatus;

      return matchesSearch && matchesStatus;
    });

    // Sort entries
    filtered.sort((a, b) => {
      let aValue, bValue;
      switch (journalSortBy) {
        case 'date':
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
          break;
        case 'amount':
          aValue = a.totalDebit;
          bValue = b.totalDebit;
          break;
        case 'entry':
          aValue = a.entryNumber;
          bValue = b.entryNumber;
          break;
        default:
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return journalSortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return journalSortOrder === 'asc' ?
        (aValue as number) - (bValue as number) :
        (bValue as number) - (aValue as number);
    });

    return filtered;
  };

  const filteredEntries = getFilteredJournalEntries();

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Journal Entries</h2>
      <table className="min-w-full bg-white border">
        <thead>
          <tr>
            <th>Entry #</th>
            <th>Date</th>
            <th>Description</th>
            <th>Reference</th>
            <th>Debit</th>
            <th>Credit</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {filteredEntries.map(entry => (
            <tr key={entry.id}>
              <td>{entry.entryNumber}</td>
              <td>{new Date(entry.date).toLocaleDateString()}</td>
              <td>{entry.description}</td>
              <td>{entry.reference}</td>
              <td>{formatCurrency(entry.totalDebit)}</td>
              <td>{formatCurrency(entry.totalCredit)}</td>
              <td>{entry.status}</td>
              <td>
                <button onClick={() => handleEditJournalEntry(entry)}>Edit</button>
                <button onClick={() => handleDeleteJournalEntry(entry.id)}>Delete</button>
                <button onClick={() => handleReverseJournalEntry(entry.id)}>Reverse</button>
                <button onClick={() => handlePostJournalEntry(entry.id)}>Post</button>
                <button onClick={() => handleViewJournalDetails(entry)}>Details</button>
                <button onClick={() => handlePrintJournalEntry(entry)}>Print</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* Add dialogs, bulk actions, filters, etc. as needed */}
    </div>
  );
};

export default JournalEntriesSection;