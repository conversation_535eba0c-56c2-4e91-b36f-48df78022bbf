/* eslint-disable @typescript-eslint/no-explicit-any */
/* src/app/dashboard/[companyId]/add-new-employee/page.tsx */
"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { UserPlus } from "lucide-react";

export default function AddEmployeePage() {
  const { companyId } = useParams();
  const router = useRouter();

  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);

  const [form, setForm] = useState({
    name: "",
    surname: "",
    email: "",
    phone: "",
    street: "",
    city: "",
    state: "",
    zip: "",
    country: "",
    role: "",
    permission: "",
    password: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => {
        const nxt = { ...prev };
        delete nxt[name];
        return nxt;
      });
    }
  };

  const validateStep = () => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!form.name.trim()) newErrors.name = "First name is required";
      if (!form.surname.trim()) newErrors.surname = "Surname is required";
      if (!form.email.trim()) newErrors.email = "Email is required";
      else if (!/^\S+@\S+\.\S+$/.test(form.email))
        newErrors.email = "Enter a valid email";
      if (!form.phone.trim()) newErrors.phone = "Phone is required";
    }

    if (step === 2) {
      if (!form.street.trim()) newErrors.street = "Street is required";
      if (!form.city.trim()) newErrors.city = "City is required";
      if (!form.state.trim()) newErrors.state = "State is required";
      if (!form.zip.trim()) newErrors.zip = "Zip code is required";
      if (!form.country.trim()) newErrors.country = "Country is required";
    }

    if (step === 3) {
      if (!form.role.trim()) newErrors.role = "Role is required";
      if (!form.permission.trim())
        newErrors.permission = "Permission level is required";
      if (!form.password) newErrors.password = "Password is required";
      else if (form.password.length < 6)
        newErrors.password = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) setStep((s) => s + 1);
  };

  const handleBack = () => {
    setErrors({});
    if (step > 1) setStep((s) => s - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep()) return;

    setLoading(true);
    try {
      const res = await fetch("/api/employees", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...form, companyId }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || "Failed to add");

      toast.success("Employee successfully added!");
      router.push(`/dashboard/${companyId}/employees`);
    } catch (err: any) {
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-6">
      <Card className="mx-auto shadow-xl dark:border-gray-700">
        <CardHeader className="flex items-center gap-3 pb-4 border-b">
          <UserPlus className="text-blue-600 dark:text-blue-400" size={28} />
          <div>
            <CardTitle className="text-2xl font-semibold text-gray-800 dark:text-white">
              Add New Employee
            </CardTitle>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Step {step} of 3
            </p>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 mt-4">
          <form onSubmit={handleSubmit} className="space-y-5">
            {step === 1 && (
              <>
                <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400">
                  Personal Information
                </h3>
                <div>
                  <Label>Name</Label>
                  <Input
                    name="name"
                    placeholder="First name"
                    value={form.name}
                    onChange={handleChange}
                  />
                  {errors.name && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.name}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Surname</Label>
                  <Input
                    name="surname"
                    placeholder="Surname"
                    value={form.surname}
                    onChange={handleChange}
                  />
                  {errors.surname && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.surname}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Email</Label>
                  <Input
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={form.email}
                    onChange={handleChange}
                  />
                  {errors.email && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.email}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Phone</Label>
                  <Input
                    name="phone"
                    placeholder="+27 65 123 4567"
                    value={form.phone}
                    onChange={handleChange}
                  />
                  {errors.phone && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.phone}
                    </p>
                  )}
                </div>
              </>
            )}

            {step === 2 && (
              <>
                <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400">
                  Address Details
                </h3>
                <div>
                  <Label>Street</Label>
                  <Input
                    name="street"
                    placeholder="123 Main St"
                    value={form.street}
                    onChange={handleChange}
                  />
                  {errors.street && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.street}
                    </p>
                  )}
                </div>
                <div>
                  <Label>City</Label>
                  <Input
                    name="city"
                    placeholder="Johannesburg"
                    value={form.city}
                    onChange={handleChange}
                  />
                  {errors.city && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.city}
                    </p>
                  )}
                </div>
                <div>
                  <Label>State</Label>
                  <Input
                    name="state"
                    placeholder="Gauteng"
                    value={form.state}
                    onChange={handleChange}
                  />
                  {errors.state && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.state}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Zip Code</Label>
                  <Input
                    name="zip"
                    placeholder="2000"
                    value={form.zip}
                    onChange={handleChange}
                  />
                  {errors.zip && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.zip}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Country</Label>
                  <Input
                    name="country"
                    placeholder="South Africa"
                    value={form.country}
                    onChange={handleChange}
                  />
                  {errors.country && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.country}
                    </p>
                  )}
                </div>
              </>
            )}

            {step === 3 && (
              <>
                <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400">
                  Role & Access
                </h3>
                <div>
                  <Label>Role</Label>
                  <Input
                    name="role"
                    placeholder="Employee, Manager..."
                    value={form.role}
                    onChange={handleChange}
                  />
                  {errors.role && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.role}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Permission</Label>
                  <select
                    name="permission"
                    value={form.permission}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border rounded-md dark:bg-gray-800 dark:text-white"
                  >
                    <option value="">Select Permission</option>
                    <option value="read">Read</option>
                    <option value="write">Write</option>
                    <option value="admin">Admin</option>
                  </select>
                  {errors.permission && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.permission}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Password</Label>
                  <Input
                    name="password"
                    type="password"
                    placeholder="Create a secure password"
                    value={form.password}
                    onChange={handleChange}
                  />
                  {errors.password && (
                    <p className="text-red-600 text-sm mt-1">
                      {errors.password}
                    </p>
                  )}
                </div>
              </>
            )}

            <div className="flex justify-between pt-4">
              {step > 1 && (
                <Button type="button" variant="outline" onClick={handleBack}>
                  Back
                </Button>
              )}
              {step < 3 && (
                <Button type="button" onClick={handleNext}>
                  Next
                </Button>
              )}
              {step === 3 && (
                <Button type="submit" disabled={loading}>
                  {loading ? "Submitting..." : "Submit"}
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
