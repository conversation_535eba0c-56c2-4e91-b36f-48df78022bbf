import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '../ui/select';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell, TableFooter } from '../ui/table';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '../ui/dropdown-menu';
import { Plus, Search, MoreHorizontal, Edit, Eye, Printer, Shuffle, CheckCircle, Trash2 } from 'lucide-react';

interface JournalEntriesProps {
  journalSearchTerm: string;
  setJournalSearchTerm: (term: string) => void;
  journalFilterStatus: string;
  setJournalFilterStatus: (status: string) => void;
  journalSortBy: string;
  setJournalSortBy: (sort: string) => void;
  journalSortOrder: string;
  setJournalSortOrder: (order: string) => void;
  journalEntries: any[];
  selectedJournalIds: string[];
  setSelectedJournalIds: (ids: string[]) => void;
  selectAllJournals: boolean;
  setSelectAllJournals: (checked: boolean) => void;
  handleViewJournalDetails: (entry: any) => void;
  handleEditJournalEntry: (entry: any) => void;
  handlePrintJournalEntry: (entry: any) => void;
  handleReverseJournalEntry: (id: string) => void;
  handlePostJournalEntry: (id: string) => void;
  handleDeleteJournalEntry: (id: string) => void;
  formatCurrency: (amount: number, currency?: string) => string;
  setShowJournalDialog: (show: boolean) => void;
}

const JournalEntires: React.FC<JournalEntriesProps> = ({
  journalSearchTerm,
  setJournalSearchTerm,
  journalFilterStatus,
  setJournalFilterStatus,
  journalSortBy,
  setJournalSortBy,
  journalSortOrder,
  setJournalSortOrder,
  journalEntries,
  selectedJournalIds,
  setSelectedJournalIds,
  selectAllJournals,
  setSelectAllJournals,
  handleViewJournalDetails,
  handleEditJournalEntry,
  handlePrintJournalEntry,
  handleReverseJournalEntry,
  handlePostJournalEntry,
  handleDeleteJournalEntry,
  formatCurrency,
  setShowJournalDialog
}) => {
  return (
    <>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Journal Entries</h2>
          <p className="text-sm text-gray-600">Manage and track all financial journal entries</p>
        </div>
        <Button onClick={() => setShowJournalDialog(true)}>
          <Plus size={16} className="mr-2" />
          New Journal Entry
        </Button>
      </div>
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Input
                  placeholder="Search journal entries..."
                  value={journalSearchTerm}
                  onChange={(e) => setJournalSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={journalFilterStatus} onValueChange={setJournalFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="posted">Posted</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="reversed">Reversed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectAllJournals}
                    onChange={(e) => {
                      const isChecked = e.target.checked;
                      setSelectAllJournals(isChecked);
                      if (isChecked) {
                        setSelectedJournalIds(journalEntries.map(e => e.id));
                      } else {
                        setSelectedJournalIds([]);
                      }
                    }}
                    aria-label="Select all journal entries"
                    className="rounded"
                  />
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setJournalSortBy('entry');
                    setJournalSortOrder(journalSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Entry # {journalSortBy === 'entry' && (journalSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setJournalSortBy('date');
                    setJournalSortOrder(journalSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Date {journalSortBy === 'date' && (journalSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setJournalSortBy('amount');
                    setJournalSortOrder(journalSortOrder === 'asc' ? 'desc' : 'asc');
                  }}
                >
                  Amount {journalSortBy === 'amount' && (journalSortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {journalEntries.map((entry) => (
                <TableRow key={entry.id} className={selectedJournalIds.includes(entry.id) ? 'bg-blue-50' : ''}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedJournalIds.includes(entry.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedJournalIds([...selectedJournalIds, entry.id]);
                        } else {
                          setSelectedJournalIds(selectedJournalIds.filter(id => id !== entry.id));
                          setSelectAllJournals(false);
                        }
                      }}
                      aria-label={`Select journal entry ${entry.entryNumber}`}
                      className="rounded"
                    />
                  </TableCell>
                  <TableCell className="font-medium">{entry.entryNumber}</TableCell>
                  <TableCell>{entry.date.toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{entry.description}</div>
                      <div className="text-sm text-gray-600">{entry.lines.length} line(s)</div>
                    </div>
                  </TableCell>
                  <TableCell>{entry.reference || '-'}</TableCell>
                  <TableCell>
                    <div className="font-medium">{formatCurrency(entry.totalDebit)}</div>
                  </TableCell>
                  <TableCell>
                    <Badge className={
                      entry.status === 'posted' ? 'bg-green-100 text-green-800' : 
                      entry.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-red-100 text-red-800'
                    }>
                      {entry.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal size={14} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleViewJournalDetails(entry)}>
                          <Eye size={14} className="mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditJournalEntry(entry)}>
                          <Edit size={14} className="mr-2" />
                          Edit Entry
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handlePrintJournalEntry(entry)}>
                          <Printer size={14} className="mr-2" />
                          Print Entry
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {entry.status === 'posted' && (
                          <DropdownMenuItem onClick={() => handleReverseJournalEntry(entry.id)}>
                            <Shuffle size={14} className="mr-2" />
                            Reverse Entry
                          </DropdownMenuItem>
                        )}
                        {entry.status === 'draft' && (
                          <DropdownMenuItem onClick={() => handlePostJournalEntry(entry.id)}>
                            <CheckCircle size={14} className="mr-2" />
                            Post Entry
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          className="text-red-600"
                          onClick={() => handleDeleteJournalEntry(entry.id)}
                        >
                          <Trash2 size={14} className="mr-2" />
                          Delete Entry
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell colSpan={8} className="text-center text-sm text-gray-500">
                  Showing {journalEntries.length} entries
                  {selectedJournalIds.length > 0 && ` • ${selectedJournalIds.length} selected`}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default JournalEntires;
